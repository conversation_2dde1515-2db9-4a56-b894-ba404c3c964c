<template>
  <div class="user-search">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户搜索</span>
        </div>
      </template>

      <!-- Search Input -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-input
              v-model="searchTerm"
              placeholder="搜索用户姓名、邮箱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          
          <el-col :span="6">
            <el-select v-model="searchType" placeholder="搜索类型" @change="handleSearch">
              <el-option label="姓名" value="name" />
              <el-option label="邮箱" value="email" />
              <el-option label="手机号" value="phone" />
              <el-option label="用户ID" value="user_id" />
            </el-select>
          </el-col>
          
          <el-col :span="6">
            <el-select v-model="profileType" placeholder="档案类型" @change="handleSearch">
              <el-option label="基础" value="basic" />
              <el-option label="动态" value="dynamic" />
              <el-option label="学生" value="student" />
              <el-option label="详细" value="rich" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-section">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
      </div>

      <!-- Results -->
      <div v-else-if="results.length > 0" class="results-section">
        <div class="results-header">
          <span>搜索结果 ({{ total }} 条)</span>
        </div>
        
        <el-table :data="results" style="width: 100%">
          <el-table-column prop="name" label="姓名" width="150" />
          <el-table-column prop="email" label="邮箱" width="200" />
          <el-table-column prop="university" label="学校" width="180" />
          <el-table-column prop="major" label="专业" width="120" />
          <el-table-column prop="identity" label="身份" width="100" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewProfile(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="searchTerm" class="empty-section">
        <el-empty description="未找到匹配的用户" />
      </div>

      <!-- Initial State -->
      <div v-else class="initial-section">
        <el-empty description="请输入搜索关键词" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useUserSearch } from '@/composables/useServerSearch'

// Search state
const searchTerm = ref('')
const searchType = ref('name')
const profileType = ref('basic')

// Initialize user search composable
const userSearch = await useUserSearch({
  searchFields: ['name', 'email', 'university'],
  sortBy: 'name',
  sortOrder: 'ASC',
  defaultLimit: 20
})

// Destructure search functionality
const {
  results,
  loading,
  error,
  total,
  currentPage,
  pageSize,
  totalPages,
  searchImmediate,
  goToPage,
  changePageSize
} = userSearch

// Search handler with debouncing
const handleSearch = () => {
  if (!searchTerm.value || !searchTerm.value.trim()) {
    results.value = []
    total.value = 0
    return
  }

  // Perform search with additional parameters
  searchImmediate(searchTerm.value, 1, {
    search_type: searchType.value,
    profile_type: profileType.value
  })
}

// Pagination handlers
const handlePageChange = (page) => {
  goToPage(page)
}

const handlePageSizeChange = (newSize) => {
  changePageSize(newSize)
}

// View user profile
const viewProfile = (user) => {
  ElMessage.info(`查看用户 ${user.name} 的详细信息`)
  // TODO: Navigate to user profile page or open modal
  console.log('View user profile:', user)
}
</script>

<style lang="scss" scoped>
.user-search {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    span {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .search-section {
    margin-bottom: 20px;
  }

  .loading-section,
  .error-section,
  .empty-section,
  .initial-section {
    margin: 20px 0;
  }

  .results-section {
    .results-header {
      margin-bottom: 16px;
      font-weight: 600;
      color: #606266;
    }

    .pagination-section {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
