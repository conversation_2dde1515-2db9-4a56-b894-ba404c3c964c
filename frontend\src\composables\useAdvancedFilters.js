import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * Composable for managing advanced filters
 * @param {Object} options - Configuration options
 * @returns {Object} Filter management functions and state
 */
export function useAdvancedFilters(options = {}) {
  const {
    initialFilters = {},
    autoApply = false,
    debounceMs = 300,
    storageKey = null,
    onFilterChange = null,
    onFilterApply = null,
    onFilterReset = null
  } = options

  // State
  const filters = reactive({
    search: '',
    dateRange: null,
    status: [],
    category: [],
    creditRange: [0, 100],
    location: [],
    sortBy: '',
    sortOrder: 'asc',
    ...initialFilters
  })

  const isLoading = ref(false)
  const hasActiveFilters = computed(() => {
    return Object.entries(filters).some(([key, value]) => {
      if (key === 'search') return value && value.trim() !== ''
      if (key === 'dateRange') return value && value.length === 2
      if (Array.isArray(value)) return value.length > 0
      if (key === 'creditRange') return value[0] !== 0 || value[1] !== 100
      return value !== null && value !== ''
    })
  })

  // Debounced filter change handler
  let debounceTimer = null
  const debouncedFilterChange = (newFilters) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    
    debounceTimer = setTimeout(() => {
      if (onFilterChange) {
        onFilterChange(newFilters)
      }
    }, debounceMs)
  }

  // Filter management methods
  const applyFilters = () => {
    isLoading.value = true
    
    try {
      const activeFilters = getActiveFilters()
      
      if (onFilterApply) {
        onFilterApply(activeFilters)
      }
      
      // Save to localStorage if storage key provided
      if (storageKey) {
        localStorage.setItem(storageKey, JSON.stringify(activeFilters))
      }
      
      ElMessage.success('筛选条件已应用')
    } catch (error) {
      ElMessage.error('应用筛选条件失败')
      console.error('Filter apply error:', error)
    } finally {
      isLoading.value = false
    }
  }

  const resetFilters = () => {
    Object.keys(filters).forEach(key => {
      if (key === 'creditRange') {
        filters[key] = [0, 100]
      } else if (Array.isArray(filters[key])) {
        filters[key] = []
      } else {
        filters[key] = key === 'sortOrder' ? 'asc' : ''
      }
    })
    
    if (onFilterReset) {
      onFilterReset()
    }
    
    // Clear from localStorage
    if (storageKey) {
      localStorage.removeItem(storageKey)
    }
    
    ElMessage.info('筛选条件已重置')
  }

  const getActiveFilters = () => {
    const activeFilters = {}
    
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'search' && value && value.trim() !== '') {
        activeFilters[key] = value.trim()
      } else if (key === 'dateRange' && value && value.length === 2) {
        activeFilters.startDate = value[0]
        activeFilters.endDate = value[1]
      } else if (Array.isArray(value) && value.length > 0) {
        activeFilters[key] = value
      } else if (key === 'creditRange' && (value[0] !== 0 || value[1] !== 100)) {
        activeFilters.minCredits = value[0]
        activeFilters.maxCredits = value[1]
      } else if (value !== null && value !== '' && key !== 'dateRange' && key !== 'creditRange') {
        activeFilters[key] = value
      }
    })
    
    return activeFilters
  }

  const setFilters = (newFilters) => {
    Object.assign(filters, newFilters)
  }

  const updateFilter = (key, value) => {
    filters[key] = value
    
    if (autoApply) {
      debouncedFilterChange(getActiveFilters())
    }
  }

  const clearFilter = (key) => {
    if (key === 'creditRange') {
      filters[key] = [0, 100]
    } else if (Array.isArray(filters[key])) {
      filters[key] = []
    } else {
      filters[key] = ''
    }
    
    if (autoApply) {
      debouncedFilterChange(getActiveFilters())
    }
  }

  // Load saved filters from localStorage
  const loadSavedFilters = () => {
    if (storageKey) {
      try {
        const saved = localStorage.getItem(storageKey)
        if (saved) {
          const savedFilters = JSON.parse(saved)
          
          // Convert back to internal format
          if (savedFilters.startDate && savedFilters.endDate) {
            savedFilters.dateRange = [savedFilters.startDate, savedFilters.endDate]
            delete savedFilters.startDate
            delete savedFilters.endDate
          }
          
          if (savedFilters.minCredits !== undefined && savedFilters.maxCredits !== undefined) {
            savedFilters.creditRange = [savedFilters.minCredits, savedFilters.maxCredits]
            delete savedFilters.minCredits
            delete savedFilters.maxCredits
          }
          
          setFilters(savedFilters)
        }
      } catch (error) {
        console.error('Error loading saved filters:', error)
      }
    }
  }

  // Filter preset management
  const presets = ref([])
  
  const savePreset = (name, filters) => {
    const preset = {
      id: Date.now().toString(),
      name,
      filters: { ...filters },
      createdAt: new Date().toISOString()
    }
    
    presets.value.push(preset)
    
    // Save presets to localStorage
    if (storageKey) {
      localStorage.setItem(`${storageKey}_presets`, JSON.stringify(presets.value))
    }
    
    ElMessage.success(`预设 "${name}" 已保存`)
    return preset
  }

  const loadPreset = (preset) => {
    setFilters(preset.filters)
    
    if (autoApply) {
      debouncedFilterChange(getActiveFilters())
    }
    
    ElMessage.success(`已加载预设 "${preset.name}"`)
  }

  const deletePreset = (preset) => {
    const index = presets.value.findIndex(p => p.id === preset.id)
    if (index > -1) {
      presets.value.splice(index, 1)
      
      // Update localStorage
      if (storageKey) {
        localStorage.setItem(`${storageKey}_presets`, JSON.stringify(presets.value))
      }
      
      ElMessage.success(`预设 "${preset.name}" 已删除`)
    }
  }

  const loadSavedPresets = () => {
    if (storageKey) {
      try {
        const saved = localStorage.getItem(`${storageKey}_presets`)
        if (saved) {
          presets.value = JSON.parse(saved)
        }
      } catch (error) {
        console.error('Error loading saved presets:', error)
      }
    }
  }

  // URL query parameter management
  const getQueryParams = () => {
    const activeFilters = getActiveFilters()
    const params = new URLSearchParams()
    
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        params.set(key, value.join(','))
      } else {
        params.set(key, value.toString())
      }
    })
    
    return params.toString()
  }

  const setFromQueryParams = (queryString) => {
    const params = new URLSearchParams(queryString)
    const newFilters = {}
    
    for (const [key, value] of params.entries()) {
      if (['status', 'category', 'location'].includes(key)) {
        newFilters[key] = value.split(',').filter(v => v.trim() !== '')
      } else if (key === 'startDate' || key === 'endDate') {
        if (!newFilters.dateRange) newFilters.dateRange = [null, null]
        newFilters.dateRange[key === 'startDate' ? 0 : 1] = value
      } else if (key === 'minCredits' || key === 'maxCredits') {
        if (!newFilters.creditRange) newFilters.creditRange = [0, 100]
        newFilters.creditRange[key === 'minCredits' ? 0 : 1] = parseInt(value)
      } else {
        newFilters[key] = value
      }
    }
    
    setFilters(newFilters)
  }

  // Watch for filter changes
  if (autoApply) {
    watch(filters, (newFilters) => {
      debouncedFilterChange(getActiveFilters())
    }, { deep: true })
  }

  // Initialize
  loadSavedFilters()
  loadSavedPresets()

  return {
    // State
    filters,
    isLoading,
    hasActiveFilters,
    presets,
    
    // Methods
    applyFilters,
    resetFilters,
    getActiveFilters,
    setFilters,
    updateFilter,
    clearFilter,
    
    // Preset management
    savePreset,
    loadPreset,
    deletePreset,
    
    // URL management
    getQueryParams,
    setFromQueryParams
  }
}
