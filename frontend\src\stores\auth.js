import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import { authApi } from '@/services/api/authApi'
import { userAdapter } from '@/services/adapters/userAdapter'
import { handleApiError, logApiCall, isDevelopment } from '@/utils/apiHelpers'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(null)
  const refreshToken = ref(null)
  const isLoggedIn = ref(false)
  const loading = ref(false)
  const error = ref(null)
  const tokenExpiry = ref(null)
  const permissions = ref([])
  const roles = ref([])

  // Getters
  const isAuthenticated = computed(() => isLoggedIn.value && !!token.value && !isTokenExpired.value)
  const userInfo = computed(() => user.value)
  // Simplified: any authenticated user has full access (admin-only system)
  const isAdmin = computed(() => isAuthenticated.value)
  const hasPermission = computed(() => () => isAuthenticated.value)
  const isTokenExpired = computed(() => {
    if (!tokenExpiry.value) return false
    return new Date() >= new Date(tokenExpiry.value)
  })

  // Actions
  const login = async (credentials) => {
    loading.value = true
    error.value = null

    try {
      logApiCall('POST', '/camp-admin/auth/login', { email: credentials.email }, null)

      // Transform credentials for camp admin API
      const loginData = userAdapter.transformLoginRequest(credentials)

      // Call real auth API (will use mock if enabled)
      const response = await authApi.login(loginData)

      if (!response.success) {
        throw new Error(response.error?.message || 'Login failed')
      }

      // Transform auth response
      const authData = userAdapter.transformAuthResponse(response.data)
      console.log('Auth Data is:',authData)

      // Store authentication data
      token.value = authData.token
      refreshToken.value = authData.refreshToken
      user.value = authData.user
      permissions.value = authData.permissions || []
      roles.value = authData.roles || []
      isLoggedIn.value = true

      // Calculate token expiry
      if (authData.expiresIn) {
        tokenExpiry.value = new Date(Date.now() + authData.expiresIn * 1000).toISOString()
      } else {
        // Set a default expiry of 24 hours if not provided
        tokenExpiry.value = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }

      // Store in cookies and localStorage
      const cookieOptions = {
        expires: credentials.remember_me ? 7 : 1, // 7 days if remember me, 1 day otherwise
        secure: !isDevelopment(),
        sameSite: 'lax'
      }

      Cookies.set('auth_token', authData.token, cookieOptions)
      if (authData.refreshToken) {
        Cookies.set('refresh_token', authData.refreshToken, { ...cookieOptions, expires: 7 })
      }

      // Store user data and auth info
      localStorage.setItem('user_data', JSON.stringify(authData.user))
      localStorage.setItem('auth_permissions', JSON.stringify(authData.permissions))
      localStorage.setItem('auth_roles', JSON.stringify(authData.roles))
      localStorage.setItem('token_expiry', tokenExpiry.value)

      logApiCall('POST', '/camp-admin/auth/login', { email: credentials.email }, { success: true })

      return { success: true, user: authData.user }
    } catch (err) {
      const normalizedError = handleApiError(err)
      error.value = normalizedError.error?.message || 'Login failed'

      logApiCall('POST', '/camp-admin/auth/login', { email: credentials.email }, normalizedError)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Registration removed - this is an admin-only system
  // Users are created by administrators, not through self-registration

  const logout = async () => {
    loading.value = true

    try {
      logApiCall('POST', '/camp-admin/auth/logout', null, null)

      // Call real auth API to invalidate token
      await authApi.logout()

      logApiCall('POST', '/camp-admin/auth/logout', null, { success: true })
    } catch (err) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', err)
      logApiCall('POST', '/camp-admin/auth/logout', null, handleApiError(err))
    }

    // Clear all auth data
    clearAuthData()

    loading.value = false
  }

  const clearAuthData = () => {
    // Clear state
    user.value = null
    token.value = null
    refreshToken.value = null
    isLoggedIn.value = false
    error.value = null
    tokenExpiry.value = null
    permissions.value = []
    roles.value = []

    // Clear stored data
    Cookies.remove('auth_token')
    Cookies.remove('refresh_token')
    localStorage.removeItem('user_data')
    localStorage.removeItem('auth_permissions')
    localStorage.removeItem('auth_roles')
    localStorage.removeItem('token_expiry')
  }

  const initializeAuth = async () => {
    // Check for stored token and data
    const storedToken = Cookies.get('auth_token')
    const storedRefreshToken = Cookies.get('refresh_token')
    const storedUser = localStorage.getItem('user_data')
    console.log(localStorage)
    const storedPermissions = localStorage.getItem('auth_permissions')
    const storedRoles = localStorage.getItem('auth_roles')
    const storedTokenExpiry = localStorage.getItem('token_expiry')

    if (storedToken && storedUser) {
      try {
        // Restore auth state
        token.value = storedToken
        refreshToken.value = storedRefreshToken
        user.value = JSON.parse(storedUser)
        permissions.value = storedPermissions ? JSON.parse(storedPermissions) : []
        roles.value = storedRoles ? JSON.parse(storedRoles) : []
        tokenExpiry.value = storedTokenExpiry
        isLoggedIn.value = true

        // Check if token is expired and try to refresh
        if (isTokenExpired.value && storedRefreshToken) {
          const refreshResult = await refreshAuthToken()
          if (!refreshResult.success) {
            clearAuthData()
            return false
          }
        }

        // Skip token verification to avoid clearing fresh login state

        return true
      } catch (err) {
        // If parsing fails, clear invalid data
        console.error('Auth initialization failed:', err)
        clearAuthData()
        return false
      }
    }

    return false
  }

  const refreshUser = async () => {
    if (!isAuthenticated.value) return { success: false, error: 'Not authenticated' }

    try {
      logApiCall('GET', '/camp-admin/auth/me', null, null)

      const response = await authApi.getCurrentUser()

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to get user profile')
      }

      // Transform and update user data
      const userData = userAdapter.transformItem(response.data, 'profile')
      user.value = userData
      localStorage.setItem('user_data', JSON.stringify(userData))

      logApiCall('GET', '/camp-admin/auth/me', null, { success: true })

      return { success: true, user: userData }
    } catch (err) {
      const normalizedError = handleApiError(err)

      logApiCall('GET', '/camp-admin/auth/me', null, normalizedError)

      // If refresh fails, logout user
      await logout()
      return { success: false, error: normalizedError.error?.message || 'Failed to refresh user' }
    }
  }

  const refreshAuthToken = async () => {
    if (!refreshToken.value) {
      return { success: false, error: 'No refresh token available' }
    }

    try {
      logApiCall('POST', '/camp-admin/auth/refresh', null, null)

      const response = await authApi.refreshToken(refreshToken.value)

      if (!response.success) {
        throw new Error(response.error?.message || 'Token refresh failed')
      }

      // Update tokens
      token.value = response.data.token
      if (response.data.refresh_token) {
        refreshToken.value = response.data.refresh_token
      }

      // Update expiry
      if (response.data.expires_in) {
        tokenExpiry.value = new Date(Date.now() + response.data.expires_in * 1000).toISOString()
        localStorage.setItem('token_expiry', tokenExpiry.value)
      }

      // Update cookies
      const cookieOptions = {
        expires: 1,
        secure: !isDevelopment(),
        sameSite: 'lax'
      }

      Cookies.set('auth_token', token.value, cookieOptions)
      if (response.data.refresh_token) {
        Cookies.set('refresh_token', response.data.refresh_token, { ...cookieOptions, expires: 7 })
      }

      logApiCall('POST', '/camp-admin/auth/refresh', null, { success: true })

      return { success: true }
    } catch (err) {
      const normalizedError = handleApiError(err)

      logApiCall('POST', '/camp-admin/auth/refresh', null, normalizedError)

      // If refresh fails, clear auth data
      clearAuthData()
      return { success: false, error: normalizedError.error?.message || 'Token refresh failed' }
    }
  }

  const verifyAuthToken = async () => {
    if (!token.value) {
      return { success: false, error: 'No token available' }
    }

    try {
      const response = await authApi.verifyToken(token.value)

      if (!response.success || !response.data.valid) {
        throw new Error('Token is invalid')
      }

      return { success: true, data: response.data }
    } catch (err) {
      const normalizedError = handleApiError(err)
      return { success: false, error: normalizedError.error?.message || 'Token verification failed' }
    }
  }

  const updateProfile = async (profileData) => {
    loading.value = true
    error.value = null

    try {
      // Note: Profile update endpoint not implemented in camp-admin auth
      // This would need to be added to the backend auth routes
      logApiCall('PUT', '/camp-admin/auth/profile', profileData, null)

      // For now, just update local user data
      const updatedUser = { ...user.value, ...profileData }
      user.value = updatedUser
      localStorage.setItem('user_data', JSON.stringify(updatedUser))

      logApiCall('PUT', '/camp-admin/auth/profile', profileData, { success: true })

      return { success: true, user: updatedUser }
    } catch (err) {
      const normalizedError = handleApiError(err)
      error.value = normalizedError.error?.message || 'Profile update failed'

      logApiCall('PUT', '/camp-admin/auth/profile', profileData, normalizedError)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData) => {
    loading.value = true
    error.value = null

    try {
      logApiCall('POST', '/camp-admin/auth/change-password', null, null)

      const response = await authApi.changePassword(passwordData)

      if (!response.success) {
        throw new Error(response.error?.message || 'Password change failed')
      }

      logApiCall('POST', '/camp-admin/auth/change-password', null, { success: true })

      return { success: true, message: response.message || 'Password changed successfully' }
    } catch (err) {
      const normalizedError = handleApiError(err)
      error.value = normalizedError.error?.message || 'Password change failed'

      logApiCall('POST', '/camp-admin/auth/change-password', null, normalizedError)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const forgotPassword = async (email) => {
    loading.value = true
    error.value = null

    try {
      logApiCall('POST', '/camp-admin/auth/forgot-password', { email }, null)

      const response = await authApi.requestPasswordReset({ email })

      logApiCall('POST', '/camp-admin/auth/forgot-password', { email }, { success: true })

      return { success: true, message: response.message || 'Password reset email sent' }
    } catch (err) {
      const normalizedError = handleApiError(err)
      error.value = normalizedError.error?.message || 'Failed to send reset email'

      logApiCall('POST', '/camp-admin/auth/forgot-password', { email }, normalizedError)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const resetPassword = async (resetData) => {
    loading.value = true
    error.value = null

    try {
      logApiCall('POST', '/camp-admin/auth/reset-password', null, null)

      const response = await authApi.resetPassword(resetData)

      if (!response.success) {
        throw new Error(response.error?.message || 'Password reset failed')
      }

      logApiCall('POST', '/camp-admin/auth/reset-password', null, { success: true })

      return { success: true, message: response.message || 'Password reset successfully' }
    } catch (err) {
      const normalizedError = handleApiError(err)
      error.value = normalizedError.error?.message || 'Password reset failed'

      logApiCall('POST', '/camp-admin/auth/reset-password', null, normalizedError)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    user,
    token,
    refreshToken,
    isLoggedIn,
    loading,
    error,
    tokenExpiry,
    permissions,
    roles,
    // Getters
    isAuthenticated,
    userInfo,
    isAdmin,
    hasPermission,
    isTokenExpired,
    // Actions
    login,
    logout,
    clearAuthData,
    initializeAuth,
    refreshUser,
    refreshAuthToken,
    verifyAuthToken,
    updateProfile,
    changePassword,
    forgotPassword,
    resetPassword
  }
})
