"""
Community Management API Router.

Handles all community-related endpoints organized by business function:
- Universities and majors listings with search and filtering
- User discovery by location, university, identity
- Content creator management
- Competition user registration and participation
- Community data search and filtering
"""

import json
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional, List
import logging
import ast
from gateway.domains.community.schemas import (
    UniversityListResponse,
    MajorListResponse,
)


from libs.auth.permissions import (
    require_user_data_access,
)
from libs.schemas.api.responses import success, ErrorResponse, SuccessResponse  
from libs.schemas.api.base import PaginationConfig
from libs.schemas.api.common import (
    SearchParams,
    SortParams,
    SortOrder,
    CommunityFilterParams
)
from core.database.dependencies import get_mongo_kesci_db

from .services import community_service

logger = logging.getLogger(__name__)

# Create router with prefix for community endpoints
router = APIRouter(
    prefix="/community",
    tags=["community"],
    responses={404: {"description": "Not found"}},
)



# ——— Universities and majors endpoints ———


@router.get(
    "/universities",
    response_model=SuccessResponse | ErrorResponse,
    summary="List all universities",
    description="Get a list of all universities in the platform with search, filtering, and sorting",
)
async def list_universities(
    # Pagination parameters
    limit: int = Query(
        -1, le=1000, description="Number of universities to return"
    ),
    offset: int = Query(-1, description="Number of universities to skip"),
    
    # Filter parameters
    country: Optional[str] = Query(None, description="Filter by country"),
    province: Optional[str] = Query(None, description="Filter by province"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for university names and locations"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (University, Country, Province)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (University, Country, Province)"),
    sort_order: SortOrder = Query(SortOrder.ASC, description="Sort order (asc/desc)"),
    
    # user_id: Optional[str] = Depends(require_user_data_access),
    database = Depends(get_mongo_kesci_db),       
):
    """List all available universities with enhanced search, filtering, and sorting."""
    try:
        # Build filter parameters
        filter_params = CommunityFilterParams(
            country=country,
            province=province
        )
        
        # Build search parameters
        search_params = None
        if search_query:
            search_params = SearchParams(
                query=search_query,
                search_fields=search_fields or ["University", "Country", "Province"],
                case_sensitive=case_sensitive
            )
        
        # Build sort parameters
        sort_params = None
        if sort_by:
            sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
        
        result = await community_service.get_universities_list(
            database=database,
            limit=limit,
            offset=offset,
            filter_params=filter_params,
            search_params=search_params,
            sort_params=sort_params
        )
        
        # Service now returns dict consistently (handles caching internally)
        return success(data=result)

    except Exception as e:
        logger.error(f"Error listing universities: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/majors",
    response_model=SuccessResponse | ErrorResponse,
    summary="List all majors",
    description="Get a list of all majors across all universities with search, filtering, and sorting",
)
async def list_majors(
    # Pagination parameters
    limit: int = Query(PaginationConfig.DEFAULT_LIMIT, ge=1, le=1000, description="Number of majors to return"),
    offset: int = Query(PaginationConfig.DEFAULT_OFFSET, ge=0, description="Number of majors to skip"),
    
    # Filter parameters
    major: Optional[str] = Query(None, description="Filter by specific major"),
    discipline: Optional[str] = Query(None, description="Filter by discipline"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for major names and disciplines"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (Major, Discipline)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (Major, Discipline)"),
    sort_order: SortOrder = Query(SortOrder.ASC, description="Sort order (asc/desc)"),
    
    database = Depends(get_mongo_kesci_db),
):
    """List all available majors with enhanced search, filtering, and sorting."""
    try:
        # Build filter parameters
        filter_params = CommunityFilterParams(
            major=major,
            discipline=discipline
        )
        
        # Build search parameters
        search_params = None
        if search_query:
            search_params = SearchParams(
                query=search_query,
                search_fields=search_fields or ["Major", "Discipline"],
                case_sensitive=case_sensitive
            )
        
        # Build sort parameters
        sort_params = None
        if sort_by:
            sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
        
        result = await community_service.get_majors_list(
            database=database,
            limit=limit,
            offset=offset,
            filter_params=filter_params,
            search_params=search_params,
            sort_params=sort_params
        )
        
        # Service now returns dict consistently (handles caching internally)
        return success(data=result)

    except Exception as e:
        logger.error(f"Error listing majors: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))



# ——— Content creator endpoints ———

# @router.post(
#     "/content_creators",
#     response_model=SuccessResponse | ErrorResponse,
#     summary="Get content creators",
#     description="Fetch content creators based on start date criteria",
# )
# async def get_content_creators(
#     creators_data: ContentCreatorsRequest = Body(...), # type: ignore
#     user_id: str = Depends(require_content_creator_access),
#     database = Depends(get_database),
# ):
#     """Get content creators based on criteria."""
#     try:
#         # Validate date format before processing.
#         validate_date_format(creators_data.start_date)

#         # Execute content creator retrieval via community service.
#         result = await community_service.get_content_creators(
#                                          database=database,
#                                          creators_data=creators_data)

#         # Handle service layer errors with proper HTTP status codes.
#         if not result.success:
#             logger.error(
#                 f"Get content creators failed: {result.error}"
#             )
#             raise HTTPException(
#                 status_code=500,
#                 detail={
#                     "message": result.error,
#                 },
#             )

#         return success(data=result.data)

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error getting content creators: {e}", exc_info=True)
#         raise HTTPException(status_code=500, detail=str(e))




