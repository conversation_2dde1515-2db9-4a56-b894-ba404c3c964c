"""
Analytics business logic and data access services.

Contains all business logic for analytics including data access,
ranking calculations, and statistical analysis.
"""

from typing import List, Dict, Any, Optional
from datetime import date, datetime
from asyncio import gather as async_gather
import os

from core.config import logger
from libs.models.tables import Route as TortoiseRoute, UserRegist<PERSON> as TortoiseUser, Qualified<PERSON>ser as TortoiseQualifiedUser
from libs.sql_utils import process_sql_template, execute_query
from libs.schemas.api.common import success_response, paginated_response, error_response


class AnalyticsServices:
    """Business logic services for analytics operations."""

    def __init__(self):
        """Initialize analytics services."""
        # Define the base path for SQL templates
        self.sql_template_dir = "gateway/domains/analytics/scripts"

    async def get_user_ranking_total(
        self, page_size: int = 20, current_page: int = 1
    ) -> Dict[str, Any]:
        """Get user's total ranking across all routes with standardized pagination."""
        routes_qs = await TortoiseRoute.all().values_list("route_id", "route_name")
        if not routes_qs:
            # Return standardized empty response
            response = success_response(
                data={
                    "total": 0,
                    "page_size": page_size,
                    "current_page": current_page,
                    "res": [],
                },
                message="No routes found"
            )
            return response.model_dump()

        case_statements = ",\n".join(
            f"MAX(CASE WHEN route_id = '{route_id}' THEN route_credits ELSE 0 END) AS credits_{i}"
            for i, (route_id, _) in enumerate(routes_qs)
        )
        select_statements = ",\n".join(
            f"rc.credits_{i}" for i, _ in enumerate(routes_qs)
        )

        replace = {
            "page_size": page_size,
            "offset": page_size * (current_page - 1),
            "case_statements": case_statements,
            "select_statements": select_statements,
        }
        template_path = os.path.join(self.sql_template_dir, "user_ranking_total.sql")
        sql = process_sql_template(template_path, replace)
        _, raw_data = await execute_query(sql)

        total = 0
        if raw_data and "total_count" in raw_data[0]:
            total = raw_data[0]["total_count"]
        else:
            # Return standardized empty response
            response = success_response(
                data={
                    "total": 0,
                    "page_size": page_size,
                    "current_page": current_page,
                    "res": [],
                },
                message="No ranking data available"
            )
            return response.model_dump()

        processed_res = []
        each_col_credits = [
            (f"credits_{i}", route_id, route_name)
            for i, (route_id, route_name) in enumerate(routes_qs)
        ]

        for d_row in raw_data:
            d_copy = dict(d_row)
            d_copy.pop("total_count", None)
            credits_each = [
                {
                    "route_name": route_name,
                    "credit": d_copy.pop(col, 0.0),
                }
                for col, _, route_name in each_col_credits
            ]
            d_copy["credits_each"] = credits_each

            credit_all_purpose = next(
                (c["credit"] for c in credits_each if c["route_name"] == "其他"), 0.0
            )

            non_other_credits = [
                c["credit"] for c in credits_each if c["route_name"] != "其他"
            ]
            max_main_credit = max(non_other_credits) if non_other_credits else 0.0
            d_copy["max_credits"] = max_main_credit + credit_all_purpose
            processed_res.append(d_copy)

        # Return standardized response
        response = success_response(
            data={
                "total": total,
                "page_size": page_size,
                "current_page": current_page,
                "res": processed_res,
            },
            message="Successfully retrieved user rankings across all routes"
        )
        return response.model_dump()

    async def get_user_ranking_by_route(self, top_n: int = 30) -> Dict[str, Any]:
        """Get user's ranking for each route with standardized response."""
        routes_qs = await TortoiseRoute.all().values_list("route_id", "route_name")
        if not routes_qs:
            # Return standardized empty response
            response = success_response(
                data=[],
                message="No routes found"
            )
            return response.model_dump()

        sqls = []
        # Filter out '其他' route as in original logic
        valid_routes = [(rid, rname) for rid, rname in routes_qs if rname != "其他"]

        for route_id, route_name in valid_routes:
            replace = {"route_id": route_id, "top_num": top_n}
            template_path = os.path.join(
                self.sql_template_dir, "user_ranking_each_route.sql"
            )
            sql = process_sql_template(template_path, replace)
            sqls.append(sql)

        if not sqls:
            # Return standardized empty response
            response = success_response(
                data=[],
                message="No valid routes found for ranking"
            )
            return response.model_dump()

        raw_results_tuples = await async_gather(*[execute_query(sql) for sql in sqls])

        processed_data: List[Dict[str, Any]] = []
        for (_, data_each_route), (_, route_name) in zip(
            raw_results_tuples, valid_routes
        ):
            tmp_d = {}
            tmp_d["route_name"] = route_name
            tmp_d["ranking"] = [
                {k: d.get(k) for k in ("user_id", "user_name", "total_credits")}
                for d in data_each_route
            ]
            processed_data.append(tmp_d)
        
        # Return standardized response
        response = success_response(
            data=processed_data,
            message="Successfully retrieved user rankings by route"
        )
        return response.model_dump()

    async def get_school_ranking(
        self, page_size: int = 20, current_page: int = 1
    ) -> Dict[str, Any]:
        """Get school ranking leaderboards with standardized pagination."""
        replace = {"page_size": page_size, "offset": page_size * (current_page - 1)}
        template_path = os.path.join(self.sql_template_dir, "school_ranking.sql")
        sql = process_sql_template(template_path, replace)
        _, raw_data = await execute_query(sql)

        total = 0
        result_raw_data = []
        if raw_data and "total_count" in raw_data[0]:
            total = raw_data[0]["total_count"]
            for d in raw_data:
                result_raw_data.append({
                    "university": d.get("university"),
                    "total_credits": d.get("total_credits"),
                })
        else:
            result_raw_data = []

        # Return standardized response
        response = success_response(
            data={
                "total": total,
                "page_size": page_size,
                "current_page": current_page,
                "res": result_raw_data,
            },
            message="Successfully retrieved school rankings"
        )
        return response.model_dump()

    async def get_topn_each_route(self, top_num: int = 10) -> Dict[str, Any]:
        """Get top N users for each route with standardized response."""
        template_path = os.path.join(self.sql_template_dir, "topn_each_route.sql")
        sql = process_sql_template(template_path, {"top_n": top_num})
        _, raw_data = await execute_query(sql)
        result_raw_data = []
        for d in raw_data:
            d_copy = dict(d)
            for k in ("user_names", "user_ids", "total_credits"):
                old_v: str = d.get(k, "")
                new_v = old_v.split(",") if old_v else []
                if k == "total_credits":
                    new_v = [float(i) if i else 0.0 for i in new_v]
                d_copy[k] = new_v
            d = d_copy
            result_raw_data.append(d)
        # Return standardized response
        response = success_response(
            data=result_raw_data,
            message=f"Successfully retrieved top {top_num} users for each route"
        )
        return response.model_dump()

    async def get_summary_statistics(
        self,
        user_id: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """Get summary statistics for analysis with standardized response."""
        template_path = os.path.join(self.sql_template_dir, "summary_statistics.sql")
        sql = process_sql_template(template_path, {})
        _, raw_data = await execute_query(sql)
        summary = []
        for d in raw_data:
            summary.append(
                {
                    "statistics_name": d.get("stats_name"),
                    "statistics_value": d.get("stats_value"),
                }
            )
        if not summary:
            # Provide default statistics if none found
            summary = [
                {"statistics_name": "total_users", "statistics_value": 0},
                {"statistics_name": "total_competitions", "statistics_value": 0},
                {"statistics_name": "total_submissions", "statistics_value": 0},
                {"statistics_name": "total_schools", "statistics_value": 0},
            ]
        
        # Return standardized response
        response = success_response(
            data=summary,
            message="Successfully retrieved summary statistics"
        )
        return response.model_dump()

    async def calculate_rankings(self, route_id: Optional[str] = None) -> None:
        """Calculate and cache ranking data for improved performance."""
        logger.info(f"Calculating rankings for route: {route_id or 'all routes'}")

    async def refresh_ranking_cache(self) -> None:
        """Refresh cached ranking data."""
        logger.info("Refreshing ranking cache")

    async def get_tags_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get analytics tags filtered by category."""
        # Placeholder implementation for analytics tags
        logger.info(f"Getting tags for category: {category}")
        return [
            {
                "tag_id": "example_tag_1",
                "name": f"{category}_tag_1",
                "category": category,
            }
        ]

    async def upload_file_to_shence(
        self, file_name_prefix: str, csv_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Upload analytics data to ShenCe platform."""
        # Placeholder implementation for ShenCe integration
        logger.info(f"Uploading file with prefix: {file_name_prefix}")
        return {
            "success": True,
            "file_name": f"{file_name_prefix}_analytics.csv",
            "file_size": len(csv_data),
            "upload_id": "mock_upload_id",
            "message": "File uploaded successfully",
        }

    async def get_shence_tag_meta(self, tag_id: str) -> Dict[str, Any]:
        """Get ShenCe tag metadata."""
        # Placeholder implementation for ShenCe tag metadata
        logger.info(f"Getting ShenCe tag metadata for: {tag_id}")
        return {
            "tag_id": tag_id,
            "name": f"tag_{tag_id}",
            "cname": f"Chinese Name for {tag_id}",
            "data_type": "STRING",
            "dir_id": "default_directory",
        }

    async def get_shence_dir_meta(self) -> Dict[str, Any]:
        """Get ShenCe directory metadata."""
        # Placeholder implementation for ShenCe directory metadata
        logger.info("Getting ShenCe directory metadata")
        return {
            "directories": [
                {"dir_id": "1", "name": "Analytics", "parent_id": None},
                {"dir_id": "2", "name": "Rankings", "parent_id": "1"},
            ],
            "total_count": 2,
            "project": "community_analytics",
        }

    async def update_shence_tags(
        self, file_name: str = "", tag_name: str = "", **kwargs
    ) -> Dict[str, Any]:
        """Update ShenCe analytics tags."""
        # Placeholder implementation for ShenCe tag updates
        logger.info(f"Updating ShenCe tag: {tag_name} with file: {file_name}")
        return {
            "success": True,
            "tag_id": kwargs.get("tag_id", "mock_tag_id"),
            "message": "Tag updated successfully",
            "update_response": {"status": "completed", "records_updated": 100},
        }

    async def query_tag_user(
        self, tag_id: str, fields: List[str], limit: int = 500, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Query users associated with a specific tag."""
        # Placeholder implementation for tag user queries
        logger.info(f"Querying users for tag: {tag_id} with fields: {fields}")
        mock_users = []
        for i in range(min(limit, 10)):  # Return up to 10 mock users
            user = {}
            for field in fields:
                user[field] = f"mock_{field}_{i}"
            mock_users.append(user)
        return mock_users

    async def get_dashboard_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive dashboard data with all relationship information."""
        # Load summary statistics
        summary_stats = await self.get_summary_statistics()
        
        # Load recent user activity if user_id provided
        user_activity = []
        if user_id:
            try:
                # Get user's recent credit history with full relationship data
                from gateway.domains.camp_admin.services import CampAdminServices
                camp_admin_service = CampAdminServices()
                credit_history = await camp_admin_service.get_credit_logs(
                    user_id=user_id, 
                    limit=10, 
                    offset=0
                )
                
                if credit_history.get('success') and credit_history.get('data', {}).get('data'):
                    user_activity = credit_history['data']['data']
                    
            except Exception as e:
                logger.warning(f"Failed to load user activity for dashboard: {e}")
        
        # Get top performing schools (limited for dashboard)
        try:
            school_rankings = await self.get_school_ranking(page_size=5, current_page=1)
            top_schools = []
            if school_rankings.get('success') and school_rankings.get('data', {}).get('res'):
                top_schools = school_rankings['data']['res']
        except Exception as e:
            logger.warning(f"Failed to load school rankings for dashboard: {e}")
            top_schools = []
        
        # Get top users by route (limited for dashboard)
        try:
            route_rankings = await self.get_user_ranking_by_route(top_n=5)
            top_users_by_route = []
            if route_rankings.get('success') and route_rankings.get('data'):
                top_users_by_route = route_rankings['data']
        except Exception as e:
            logger.warning(f"Failed to load route rankings for dashboard: {e}")
            top_users_by_route = []
        
        # Compile comprehensive dashboard data
        dashboard_data = {
            "summary_statistics": summary_stats.get('data', []),
            "user_activity": user_activity,
            "top_schools": top_schools,
            "top_users_by_route": top_users_by_route,
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "user_id": user_id,
                "has_user_data": bool(user_id and user_activity)
            }
        }
        
        return success_response(
            data=dashboard_data,
            message="Dashboard data loaded successfully with full relationship information"
        ).model_dump()

    async def get_user_profile_complete(self, user_id: str) -> Dict[str, Any]:
        """Get complete user profile with all relationship data."""
        try:
            # Get user registrations with full data
            user_registrations = await TortoiseUser.filter(
                user_id=user_id,
                is_deleted=False
            ).values(
                "user_id", "user_name", "university", "email", "phone",
                "competition_id", "competition_name", "register_ts"
            )
            
            if not user_registrations:
                response = error_response(
                    message=f"User {user_id} not found or has no registrations"
                )
                return response.model_dump()
            
            # Get credit history with relationship data
            credit_history = await TortoiseQualifiedUser.filter(
                user_id=user_id
            ).values(
                "credit", "qualification_ts", "qualification_name", 
                "competition_name", "university"
            )
            
            # Get user qualifications
            qualifications = await TortoiseQualifiedUser.filter(
                user_id=user_id
            ).distinct().values("qualification_id", "qualification_name")
            
            # Calculate aggregated statistics
            total_credits = sum(c.get("credit", 0) for c in credit_history)
            universities = set(r.get("university") for r in user_registrations if r.get("university"))
            competitions = set(r.get("competition_id") for r in user_registrations if r.get("competition_id"))
            
            profile_data = {
                "user_info": user_registrations[0] if user_registrations else {},
                "registrations": user_registrations,
                "credit_history": credit_history,
                "qualifications": qualifications,
                "statistics": {
                    "total_credits": total_credits,
                    "universities_count": len(universities),
                    "competitions_count": len(competitions),
                    "qualifications_count": len(qualifications)
                }
            }
            
            response = success_response(
                data=profile_data,
                message=f"Successfully retrieved complete profile for user {user_id}"
            )
            return response.model_dump()
            
        except Exception as e:
            logger.warning(f"Error getting user profile for {user_id}: {e}")
            response = error_response(
                message=f"Failed to retrieve user profile: {str(e)}"
            )
            return response.model_dump()

    # ——— Phase 2.3 - New Missing Endpoints ———

    async def get_competition_stats(self, competition_id: str) -> Dict[str, Any]:
        """Get comprehensive statistics for a specific competition."""
        try:
            from libs.models.tables import (
                Competition as TortoiseCompetition,
                UserRegistration as TortoiseUserRegistration,
                Submission as TortoiseSubmission,
                QualifiedUser as TortoiseQualifiedUser
            )
            
            # Get competition basic info
            competition = await TortoiseCompetition.filter(
                competition_id=competition_id,
                is_deleted=False
            ).first()
            
            if not competition:
                response = error_response(
                    message=f"Competition {competition_id} not found"
                )
                return response.model_dump()
            
            # Get participant count
            participant_count = await TortoiseUserRegistration.filter(
                competition_id=competition_id,
                is_deleted=False
            ).count()
            
            # Get submission count and score statistics
            submissions = await TortoiseSubmission.filter(
                competition_id=competition_id
            ).values("score")
            
            submission_count = len(submissions)
            scores = [s["score"] for s in submissions if s["score"] is not None]
            average_score = sum(scores) / len(scores) if scores else None
            top_score = max(scores) if scores else None
            
            # Get qualified users and credits
            qualified_users = await TortoiseQualifiedUser.filter(
                competition_id=competition_id
            ).values("credit")
            
            qualified_user_count = len(qualified_users)
            total_credits_awarded = sum(q["credit"] for q in qualified_users)
            
            stats_data = {
                "competition_id": competition_id,
                "competition_name": competition.competition_name,
                "route_name": getattr(competition, "route_name", "未知赛道"),
                "participant_count": participant_count,
                "submission_count": submission_count,
                "qualified_user_count": qualified_user_count,
                "total_credits_awarded": float(total_credits_awarded),
                "average_score": average_score,
                "top_score": top_score,
                "start_date": competition.start_date,
                "end_date": competition.end_date
            }
            
            response = success_response(
                data=stats_data,
                message=f"Successfully retrieved statistics for competition {competition_id}"
            )
            return response.model_dump()
            
        except Exception as e:
            logger.error(f"Error getting competition stats for {competition_id}: {e}")
            response = error_response(
                message=f"Failed to retrieve competition statistics: {str(e)}"
            )
            return response.model_dump()

    async def get_school_stats(self, university: str) -> Dict[str, Any]:
        """Get comprehensive statistics for a specific school."""
        try:
            from libs.models.tables import (
                UserRegistration as TortoiseUserRegistration,
                QualifiedUser as TortoiseQualifiedUser
            )
            
            # Get student count
            students = await TortoiseUserRegistration.filter(
                university=university,
                is_deleted=False
            ).distinct().values("user_id", "user_name")
            
            student_count = len(students)
            
            if student_count == 0:
                response = error_response(
                    message=f"No students found for university {university}"
                )
                return response.model_dump()
            
            # Get credits and qualifications for school
            school_credits = await TortoiseQualifiedUser.filter(
                university=university
            ).values("credit", "route_name", "competition_id", "qualification_ts")
            
            total_credits = sum(c["credit"] for c in school_credits)
            average_credits_per_student = total_credits / student_count if student_count > 0 else 0.0
            
            # Get competition participation
            participations = await TortoiseUserRegistration.filter(
                university=university,
                is_deleted=False
            ).values("competition_id", "user_id")
            
            competition_participation_count = len(participations)
            qualified_students_count = len(set(c.get("user_id") for c in school_credits if c.get("user_id")))
            
            # Aggregate top routes
            route_credits = {}
            for credit in school_credits:
                route = credit.get("route_name", "其他")
                route_credits[route] = route_credits.get(route, 0) + credit.get("credit", 0)
            
            top_routes = [
                {"route_name": route, "total_credits": credits}
                for route, credits in sorted(route_credits.items(), key=lambda x: x[1], reverse=True)[:5]
            ]
            
            # Recent activity (last 10 qualifications)
            recent_activity = sorted(
                school_credits,
                key=lambda x: x.get("qualification_ts") or datetime.min,
                reverse=True
            )[:10]
            
            stats_data = {
                "university": university,
                "student_count": student_count,
                "total_credits": float(total_credits),
                "average_credits_per_student": float(average_credits_per_student),
                "competition_participation_count": competition_participation_count,
                "qualified_students_count": qualified_students_count,
                "top_routes": top_routes,
                "recent_activity": recent_activity
            }
            
            response = success_response(
                data=stats_data,
                message=f"Successfully retrieved statistics for {university}"
            )
            return response.model_dump()
            
        except Exception as e:
            logger.error(f"Error getting school stats for {university}: {e}")
            response = error_response(
                message=f"Failed to retrieve school statistics: {str(e)}"
            )
            return response.model_dump()

    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive statistics for a specific user."""
        try:
            from libs.models.tables import (
                UserRegistration as TortoiseUserRegistration,
                QualifiedUser as TortoiseQualifiedUser,
                Submission as TortoiseSubmission
            )
            
            # Get user basic info
            user_info = await TortoiseUserRegistration.filter(
                user_id=user_id,
                is_deleted=False
            ).first()
            
            if not user_info:
                response = error_response(
                    message=f"User {user_id} not found"
                )
                return response.model_dump()
            
            # Get user qualifications and credits
            qualifications = await TortoiseQualifiedUser.filter(
                user_id=user_id
            ).values("credit", "route_name", "qualification_name", "competition_name", "qualification_ts")
            
            total_credits = sum(q["credit"] for q in qualifications)
            qualifications_earned = len(qualifications)
            
            # Get competition participation
            competitions = await TortoiseUserRegistration.filter(
                user_id=user_id,
                is_deleted=False
            ).distinct().values("competition_id", "competition_name")
            
            competitions_participated = len(competitions)
            
            # Get submission statistics  
            submissions = await TortoiseSubmission.filter(
                user_id=user_id
            ).values("score", "submission_ts")
            
            submissions_count = len(submissions)
            scores = [s["score"] for s in submissions if s["score"] is not None]
            average_score = sum(scores) / len(scores) if scores else None
            best_score = max(scores) if scores else None
            
            # Route performance
            route_performance = {}
            for qual in qualifications:
                route = qual.get("route_name", "其他")
                if route not in route_performance:
                    route_performance[route] = {"credits": 0, "qualifications": 0}
                route_performance[route]["credits"] += qual.get("credit", 0)
                route_performance[route]["qualifications"] += 1
            
            route_performance_list = [
                {
                    "route_name": route,
                    "total_credits": perf["credits"],
                    "qualifications_count": perf["qualifications"]
                }
                for route, perf in route_performance.items()
            ]
            
            # Recent activities (last 10 qualifications)
            recent_activities = sorted(
                qualifications,
                key=lambda x: x.get("qualification_ts") or datetime.min,
                reverse=True
            )[:10]
            
            stats_data = {
                "user_id": user_id,
                "user_name": getattr(user_info, "user_name", "未知用户"),
                "university": getattr(user_info, "university", None),
                "total_credits": float(total_credits),
                "competitions_participated": competitions_participated,
                "qualifications_earned": qualifications_earned,
                "submissions_count": submissions_count,
                "average_score": average_score,
                "best_score": best_score,
                "route_performance": route_performance_list,
                "recent_activities": recent_activities,
                "rank_position": None  # Would need complex query to determine rank
            }
            
            response = success_response(
                data=stats_data,
                message=f"Successfully retrieved statistics for user {user_id}"
            )
            return response.model_dump()
            
        except Exception as e:
            logger.error(f"Error getting user stats for {user_id}: {e}")
            response = error_response(
                message=f"Failed to retrieve user statistics: {str(e)}"
            )
            return response.model_dump()

    async def get_count(self, category: str) -> Dict[str, Any]:
        """Get count for various categories (competitions, users, schools, etc.)."""
        try:
            from libs.models.tables import (
                Competition as TortoiseCompetition,
                UserRegistration as TortoiseUserRegistration,
                Route as TortoiseRoute,
                QualifiedUser as TortoiseQualifiedUser
            )
            
            count = 0
            
            if category == "competitions":
                count = await TortoiseCompetition.filter(is_deleted=False).count()
            elif category == "users":
                count = await TortoiseUserRegistration.filter(is_deleted=False).distinct().count("user_id")
            elif category == "schools":
                # Count distinct universities
                universities = await TortoiseUserRegistration.filter(
                    is_deleted=False,
                    university__isnull=False
                ).distinct().values_list("university", flat=True)
                count = len(set(universities))
            elif category == "routes":
                count = await TortoiseRoute.filter(is_deleted=False).count()
            elif category == "qualifications":
                count = await TortoiseQualifiedUser.all().count()
            else:
                response = error_response(
                    message=f"Unknown category: {category}. Supported: competitions, users, schools, routes, qualifications"
                )
                return response.model_dump()
            
            count_data = {
                "count": count,
                "category": category
            }
            
            response = success_response(
                data=count_data,
                message=f"Successfully retrieved count for {category}"
            )
            return response.model_dump()
            
        except Exception as e:
            logger.error(f"Error getting count for {category}: {e}")
            response = error_response(
                message=f"Failed to retrieve count: {str(e)}"
            )
            return response.model_dump()


# Global services instance
services = AnalyticsServices()
