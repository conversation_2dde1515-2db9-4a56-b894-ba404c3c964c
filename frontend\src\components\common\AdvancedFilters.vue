<template>
  <div class="advanced-filters">
    <el-card>
      <template #header>
        <div class="filter-header">
          <span>高级筛选</span>
          <el-button 
            type="text" 
            @click="toggleExpanded"
            :icon="expanded ? ArrowUp : ArrowDown"
          >
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>

      <div v-show="expanded" class="filter-content">
        <el-form :model="filters" label-width="100px" @submit.prevent>
          <el-row :gutter="16">
            <!-- Date Range Filter -->
            <el-col :span="8" v-if="showDateFilter">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="filters.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-col>

            <!-- Status Filter -->
            <el-col :span="8" v-if="showStatusFilter">
              <el-form-item label="状态">
                <el-select
                  v-model="filters.status"
                  placeholder="选择状态"
                  clearable
                  multiple
                  @change="handleStatusChange"
                >
                  <el-option
                    v-for="status in statusOptions"
                    :key="status.value"
                    :label="status.label"
                    :value="status.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- Category Filter -->
            <el-col :span="8" v-if="showCategoryFilter">
              <el-form-item label="分类">
                <el-select
                  v-model="filters.category"
                  placeholder="选择分类"
                  clearable
                  multiple
                  @change="handleCategoryChange"
                >
                  <el-option
                    v-for="category in categoryOptions"
                    :key="category.value"
                    :label="category.label"
                    :value="category.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- Credit Range Filter -->
            <el-col :span="8" v-if="showCreditFilter">
              <el-form-item label="学分范围">
                <el-slider
                  v-model="filters.creditRange"
                  range
                  :min="creditRange.min"
                  :max="creditRange.max"
                  :step="creditRange.step"
                  show-input
                  @change="handleCreditChange"
                />
              </el-form-item>
            </el-col>

            <!-- Location Filter -->
            <el-col :span="8" v-if="showLocationFilter">
              <el-form-item label="地区">
                <el-cascader
                  v-model="filters.location"
                  :options="locationOptions"
                  placeholder="选择地区"
                  clearable
                  @change="handleLocationChange"
                />
              </el-form-item>
            </el-col>

            <!-- Custom Filters -->
            <el-col 
              v-for="customFilter in customFilters" 
              :key="customFilter.key"
              :span="customFilter.span || 8"
            >
              <el-form-item :label="customFilter.label">
                <component
                  :is="customFilter.component"
                  v-model="filters[customFilter.key]"
                  v-bind="customFilter.props"
                  @change="handleCustomFilterChange(customFilter.key, $event)"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- Action Buttons -->
          <el-row>
            <el-col :span="24" class="filter-actions">
              <el-button @click="handleReset">重置</el-button>
              <el-button type="primary" @click="handleApply">应用筛选</el-button>
              <el-button type="info" @click="handleSavePreset" v-if="allowPresets">保存预设</el-button>
            </el-col>
          </el-row>

          <!-- Filter Presets -->
          <el-row v-if="allowPresets && presets.length > 0">
            <el-col :span="24">
              <div class="filter-presets">
                <span class="preset-label">快速筛选:</span>
                <el-tag
                  v-for="preset in presets"
                  :key="preset.id"
                  :type="preset.active ? 'primary' : 'info'"
                  class="preset-tag"
                  @click="handleLoadPreset(preset)"
                  closable
                  @close="handleDeletePreset(preset)"
                >
                  {{ preset.name }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // Filter visibility controls
  showDateFilter: {
    type: Boolean,
    default: true
  },
  showStatusFilter: {
    type: Boolean,
    default: true
  },
  showCategoryFilter: {
    type: Boolean,
    default: false
  },
  showCreditFilter: {
    type: Boolean,
    default: false
  },
  showLocationFilter: {
    type: Boolean,
    default: false
  },
  
  // Filter options
  statusOptions: {
    type: Array,
    default: () => [
      { label: '活跃', value: 'active' },
      { label: '待处理', value: 'pending' },
      { label: '已完成', value: 'completed' },
      { label: '已取消', value: 'cancelled' }
    ]
  },
  categoryOptions: {
    type: Array,
    default: () => []
  },
  locationOptions: {
    type: Array,
    default: () => []
  },
  
  // Credit range configuration
  creditRange: {
    type: Object,
    default: () => ({
      min: 0,
      max: 100,
      step: 1
    })
  },
  
  // Custom filters
  customFilters: {
    type: Array,
    default: () => []
  },
  
  // Preset functionality
  allowPresets: {
    type: Boolean,
    default: false
  },
  
  // Initial values
  initialFilters: {
    type: Object,
    default: () => ({})
  },
  
  // Auto-apply filters
  autoApply: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'filter-change',
  'filter-apply',
  'filter-reset',
  'preset-save',
  'preset-load',
  'preset-delete'
])

// State
const expanded = ref(false)
const presets = ref([])

const filters = reactive({
  dateRange: null,
  status: [],
  category: [],
  creditRange: [props.creditRange.min, props.creditRange.max],
  location: [],
  ...props.initialFilters
})

// Methods
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const handleDateChange = (value) => {
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleStatusChange = (value) => {
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleCategoryChange = (value) => {
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleCreditChange = (value) => {
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleLocationChange = (value) => {
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleCustomFilterChange = (key, value) => {
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleApply = () => {
  emit('filter-apply', { ...filters })
}

const handleReset = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'creditRange') {
      filters[key] = [props.creditRange.min, props.creditRange.max]
    } else if (Array.isArray(filters[key])) {
      filters[key] = []
    } else {
      filters[key] = null
    }
  })
  
  emit('filter-reset')
  
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleSavePreset = () => {
  emit('preset-save', { ...filters })
}

const handleLoadPreset = (preset) => {
  Object.assign(filters, preset.filters)
  emit('preset-load', preset)
  
  if (props.autoApply) {
    emit('filter-change', { ...filters })
  }
}

const handleDeletePreset = (preset) => {
  emit('preset-delete', preset)
}

// Watch for external filter changes
watch(() => props.initialFilters, (newFilters) => {
  Object.assign(filters, newFilters)
}, { deep: true })

// Expose methods for parent components
defineExpose({
  applyFilters: handleApply,
  resetFilters: handleReset,
  getFilters: () => ({ ...filters }),
  setFilters: (newFilters) => Object.assign(filters, newFilters)
})
</script>

<style scoped>
.advanced-filters {
  margin-bottom: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-content {
  padding-top: 16px;
}

.filter-actions {
  text-align: right;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.filter-presets {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.preset-label {
  margin-right: 8px;
  color: #606266;
  font-size: 14px;
}

.preset-tag {
  margin-right: 8px;
  margin-bottom: 4px;
  cursor: pointer;
}

.preset-tag:hover {
  opacity: 0.8;
}
</style>
