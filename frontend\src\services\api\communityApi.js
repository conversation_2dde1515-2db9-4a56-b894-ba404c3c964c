/**
 * Community API Service
 * Handles university and major data from the Community API
 */

import { api } from '../api.js'
import { handleApiResponse, handleApiError, buildQueryString, logApiCall } from '../../utils/apiHelpers.js'

const BASE_PATH = '/community'

/**
 * Get list of universities with server-side search and filtering
 * @param {Object} params - Query parameters
 * @param {string} params.q - Search query
 * @param {string} params.search_fields - Comma-separated search fields (University,Country,Province)
 * @param {string} params.sort_by - Sort field
 * @param {string} params.sort_order - Sort order (ASC/DESC)
 * @param {number} params.limit - Number of universities to return (max: 1000, default: -1 for all)
 * @param {number} params.offset - Number of universities to skip (default: -1)
 * @param {string} params.country - Filter by country
 * @param {string} params.province - Filter by province
 * @param {boolean} params.case_sensitive - Case sensitive search (default: false)
 * @returns {Promise<Object>} Universities list response
 */
export const getUniversities = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/universities${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data,
      total: result.pagination?.total,
      limit: result.pagination?.limit,
      offset: result.pagination?.offset
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/universities`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get list of majors
 * @param {Object} params - Query parameters
 * @param {number} params.limit - Number of majors to return (1-1000, default: 20)
 * @param {number} params.offset - Number of majors to skip (default: 0)
 * @returns {Promise<Object>} Majors list response
 */
export const getMajors = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/majors${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data,
      total: result.pagination?.total,
      limit: result.pagination?.limit,
      offset: result.pagination?.offset
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/majors`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get content creators (currently disabled in backend)
 * @param {Object} data - Request data
 * @param {string} data.start_date - Start date for filtering
 * @returns {Promise<Object>} Content creators response
 */
export const getContentCreators = async (data) => {
  try {
    const url = `${BASE_PATH}/content_creators`
    
    logApiCall('POST', url, data, null)
    
    const response = await api.post(url, data)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, data, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/content_creators`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get universities by country
 * @param {string} country - Country name
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Universities list response
 */
export const getUniversitiesByCountry = async (country, additionalParams = {}) => {
  return getUniversities({
    country,
    ...additionalParams
  })
}

/**
 * Get universities by province
 * @param {string} province - Province name
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Universities list response
 */
export const getUniversitiesByProvince = async (province, additionalParams = {}) => {
  return getUniversities({
    province,
    ...additionalParams
  })
}

/**
 * Get all universities (no pagination)
 * @returns {Promise<Object>} All universities response
 */
export const getAllUniversities = async () => {
  return getUniversities({ limit: -1, offset: -1 })
}

/**
 * Search universities using server-side search with fallback to client-side
 * @param {string} searchTerm - Search term
 * @param {Object} additionalParams - Additional query parameters
 * @param {string} additionalParams.search_fields - Fields to search in (default: 'University,Country,Province')
 * @param {string} additionalParams.sort_by - Sort field (default: 'University')
 * @param {string} additionalParams.sort_order - Sort order (default: 'ASC')
 * @param {number} additionalParams.limit - Number of results (default: 20)
 * @param {number} additionalParams.offset - Offset for pagination (default: 0)
 * @returns {Promise<Object>} Search results response
 */
export const searchUniversities = async (searchTerm, additionalParams = {}) => {
  try {
    // Use server-side search if search term is provided
    if (searchTerm && searchTerm.trim()) {
      const searchParams = {
        q: searchTerm.trim(),
        search_fields: additionalParams.search_fields || 'University,Country,Province',
        sort_by: additionalParams.sort_by || 'University',
        sort_order: additionalParams.sort_order || 'ASC',
        limit: additionalParams.limit || 20,
        offset: additionalParams.offset || 0,
        case_sensitive: additionalParams.case_sensitive || false,
        ...additionalParams
      }

      console.log('🔍 Using server-side university search:', searchParams)
      return await getUniversities(searchParams)
    }

    // Fallback to client-side filtering for backward compatibility
    console.log('🔍 Using client-side university search fallback')
    const response = await getAllUniversities()

    if (!response.success) {
      return response
    }

    let filteredData = response.data

    // Apply additional filters if provided
    if (additionalParams.country) {
      filteredData = filteredData.filter(university =>
        university.country === additionalParams.country
      )
    }

    if (additionalParams.province) {
      filteredData = filteredData.filter(university =>
        university.province === additionalParams.province
      )
    }

    return {
      success: true,
      data: filteredData,
      total: filteredData.length,
      searchTerm
    }
  } catch (error) {
    console.error('Error searching universities:', error)
    throw error
  }
}

/**
 * Get university statistics (derived from analytics API)
 * This would typically call the analytics API for university-specific stats
 * @param {string} universityId - University ID
 * @returns {Promise<Object>} University statistics
 */
export const getUniversityStats = async (universityId) => {
  // This would be implemented when analytics API is integrated
  // For now, return a placeholder structure
  return {
    success: true,
    data: {
      university_id: universityId,
      total_students: 0,
      active_students: 0,
      total_competitions: 0,
      total_submissions: 0,
      average_score: 0
    }
  }
}

// Export all functions as a service object
export const communityApi = {
  getUniversities,
  getMajors,
  getContentCreators,
  getUniversitiesByCountry,
  getUniversitiesByProvince,
  getAllUniversities,
  searchUniversities,
  getUniversityStats
}
