<template>
  <div class="school-statistics">
    <div class="page-header">
      <h1>学校统计</h1>
      <p class="page-description">学校/学生统计</p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
      <el-button @click="loadData" type="primary" style="margin-top: 16px">
        Retry
      </el-button>
    </div>

    <!-- Content -->
    <div v-else class="statistics-content">
      <!-- Summary Cards -->
      <div class="summary-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="summary-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon><School /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ stats.totalSchools || 0 }}</div>
                  <div class="card-label">总参与学校</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="summary-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ formatNumber(stats.totalStudents) || 0 }}</div>
                  <div class="card-label">总学生人数</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="summary-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ stats.averageParticipationRate?.toFixed(1) || 0 }}%</div>
                  <div class="card-label">总学分获得</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="summary-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ stats.topPerformingSchools?.length || 0 }}</div>
                  <div class="card-label">总通关人数</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- Charts and Tables -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- Top Performing Schools -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>学校排行榜</span>
              </div>
            </template>

            <div v-if="stats.topPerformingSchools?.length > 0">
              <div
                v-for="(item, index) in stats.topPerformingSchools.slice(0, 10)"
                :key="item.school.id"
                class="ranking-item"
              >
                <div class="rank">{{ item.rank }}</div>
                <div class="school-info">
                  <div class="school-name">{{ item.school.name }}</div>
                  <div class="school-metrics">{{ item.metrics.displayCredits || `${item.score} credits` }}</div>
                </div>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="No ranking data available" />
            </div>
          </el-card>
        </el-col>

        <!-- Geographic Distribution -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>学校分布</span>
              </div>
            </template>

            <div v-if="Object.keys(stats.geographicDistribution || {}).length > 0">
              <div
                v-for="(count, country) in stats.geographicDistribution"
                :key="country"
                class="distribution-item"
              >
                <div class="distribution-label">{{ country }}</div>
                <div class="distribution-bar">
                  <div
                    class="distribution-fill"
                    :style="{ width: `${(count / stats.totalSchools) * 100}%` }"
                  ></div>
                </div>
                <div class="distribution-value">{{ count }}</div>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="No geographic data available" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- Schools List -->
      <el-card style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <span>参与学校列表</span>
            <el-button @click="loadSchools" :loading="schoolsLoading">
              刷新
            </el-button>
          </div>
        </template>

        <div class="schools-filters">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-input
                v-model="schoolsParams.search"
                placeholder="Search universities..."
                clearable
                @input="debouncedSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-select v-model="schoolsParams.country" placeholder="Country" clearable @change="loadSchools">
                <el-option
                  v-for="country in availableCountries"
                  :key="country"
                  :label="country"
                  :value="country"
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="schoolsParams.province" placeholder="Province" clearable @change="loadSchools">
                <el-option
                  v-for="province in availableProvinces"
                  :key="province"
                  :label="province"
                  :value="province"
                />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <el-table
          :data="schools"
          v-loading="schoolsLoading"
          style="width: 100%; margin-top: 16px;"
        >
          <el-table-column prop="name" label="University Name" min-width="200">
            <template #default="{ row }">
              <div class="university-cell">
                <div class="university-name">{{ row.displayName || row.name }}</div>
                <div class="university-location">{{ row.location }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="country" label="Country" width="120" />
          <el-table-column prop="province" label="Province" width="120" />
          <el-table-column label="Students" width="100">
            <template #default="{ row }">
              {{ formatNumber(row.stats?.totalStudents || 0) }}
            </template>
          </el-table-column>
          <el-table-column label="Participation" width="120">
            <template #default="{ row }">
              {{ (row.stats?.participationRate || 0).toFixed(1) }}%
            </template>
          </el-table-column>
          <el-table-column label="Avg Score" width="100">
            <template #default="{ row }">
              {{ (row.stats?.averageScore || 0).toFixed(1) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="schoolsParams.page"
            v-model:page-size="schoolsParams.limit"
            :page-sizes="[10, 20, 50, 100]"
            :total="schoolsPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadSchools"
            @current-change="loadSchools"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { School, User, TrendCharts, Trophy, Search } from '@element-plus/icons-vue'
import { schoolService } from '@/services/schoolService'
import { debounce } from 'lodash-es'

// State
const loading = ref(true)
const error = ref(null)
const stats = ref({})
const schools = ref([])
const schoolsLoading = ref(false)

// Pagination and filters
const schoolsParams = reactive({
  page: 1,
  limit: 20,
  search: '',
  country: '',
  province: ''
})

const schoolsPagination = reactive({
  total: 0,
  pages: 0,
  hasNext: false,
  hasPrev: false
})

// Computed
const availableCountries = computed(() => {
  const countries = new Set()
  schools.value.forEach(school => {
    if (school.country) countries.add(school.country)
  })
  return Array.from(countries).sort()
})

const availableProvinces = computed(() => {
  const provinces = new Set()
  schools.value.forEach(school => {
    if (school.province) provinces.add(school.province)
  })
  return Array.from(provinces).sort()
})

// Methods - Updated to use backend display fields with fallbacks
const formatNumber = (num) => {
  // Fallback for older data that doesn't have display fields
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// Helper functions to use display fields with fallbacks
const getDisplayName = (school) => {
  return school.display_name || school.display_full_name || school.university || school.name || 'Unknown School'
}

const getDisplayLocation = (school) => {
  return school.display_location || `${school.province || 'Unknown'}, ${school.country || 'Unknown'}`
}

const getDisplayCredits = (school) => {
  return school.display_credits || formatNumber(school.total_credits || school.credits || 0)
}

const getDisplayStatus = (school) => {
  return school.display_status || school.status || 'Unknown'
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load statistics
    const statsResponse = await schoolService.getSchoolStats()

    if (statsResponse.success) {
      stats.value = statsResponse.data
    } else {
      throw new Error(statsResponse.error?.message || 'Failed to load statistics')
    }

    // Load initial schools list
    await loadSchools()

  } catch (err) {
    console.error('Failed to load school statistics:', err)
    error.value = err.message || 'Failed to load data'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

const loadSchools = async () => {
  schoolsLoading.value = true

  try {
    const response = await schoolService.getSchools(schoolsParams)

    if (response.success) {
      schools.value = response.data
      schoolsPagination.total = response.pagination?.total || 0
      schoolsPagination.pages = response.pagination?.pages || 0
      schoolsPagination.hasNext = response.pagination?.hasNext || false
      schoolsPagination.hasPrev = response.pagination?.hasPrev || false
    } else {
      throw new Error(response.error?.message || 'Failed to load schools')
    }
  } catch (err) {
    console.error('Failed to load schools:', err)
    ElMessage.error(err.message || 'Failed to load schools')
  } finally {
    schoolsLoading.value = false
  }
}

// Debounced search
const debouncedSearch = debounce(() => {
  schoolsParams.page = 1 // Reset to first page
  loadSchools()
}, 500)

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .page-description {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.summary-cards {
  .summary-card {
    .card-content {
      display: flex;
      align-items: center;

      .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .el-icon {
          font-size: 24px;
          color: white;
        }
      }

      .card-info {
        flex: 1;

        .card-value {
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          line-height: 1;
        }

        .card-label {
          font-size: 14px;
          color: #7f8c8d;
          margin-top: 4px;
        }
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .rank {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #495057;
    margin-right: 12px;
  }

  .school-info {
    flex: 1;

    .school-name {
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .school-metrics {
      font-size: 12px;
      color: #7f8c8d;
    }
  }
}

.distribution-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .distribution-label {
    width: 80px;
    font-size: 14px;
    color: #495057;
  }

  .distribution-bar {
    flex: 1;
    height: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 0 12px;
    overflow: hidden;

    .distribution-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      transition: width 0.3s ease;
    }
  }

  .distribution-value {
    width: 40px;
    text-align: right;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
  }
}

.schools-filters {
  margin-bottom: 16px;
}

.university-cell {
  .university-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
  }

  .university-location {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.no-data {
  padding: 40px 0;
  text-align: center;
}
</style>
