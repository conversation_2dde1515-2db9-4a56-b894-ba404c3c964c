import { ref, computed } from 'vue'
import { debounce } from 'lodash-es'

/**
 * Composable for server-side search with debouncing
 * @param {Function} searchFunction - The API function to call for search
 * @param {Object} options - Configuration options
 * @param {number} options.debounceMs - Debounce delay in milliseconds (default: 300)
 * @param {number} options.minSearchLength - Minimum search term length (default: 2)
 * @param {Array} options.searchFields - Default search fields
 * @param {string} options.sortBy - Default sort field
 * @param {string} options.sortOrder - Default sort order
 * @param {number} options.defaultLimit - Default page size
 * @returns {Object} Search state and methods
 */
export function useServerSearch(searchFunction, options = {}) {
  const {
    debounceMs = 300,
    minSearchLength = 2,
    searchFields = [],
    sortBy = '',
    sortOrder = 'ASC',
    defaultLimit = 20
  } = options

  // Reactive state
  const searchTerm = ref('')
  const results = ref([])
  const loading = ref(false)
  const error = ref(null)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(defaultLimit)

  // Computed properties
  const hasResults = computed(() => results.value.length > 0)
  const hasError = computed(() => error.value !== null)
  const isEmpty = computed(() => !loading.value && !hasResults.value && searchTerm.value.length >= minSearchLength)
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

  // Clear results and error
  const clearResults = () => {
    results.value = []
    error.value = null
    total.value = 0
    currentPage.value = 1
  }

  // Perform search
  const performSearch = async (term = searchTerm.value, page = 1) => {
    if (!term || term.length < minSearchLength) {
      clearResults()
      return
    }

    loading.value = true
    error.value = null

    try {
      const searchParams = {
        q: term,
        limit: pageSize.value,
        offset: (page - 1) * pageSize.value
      }

      // Add search fields if provided
      if (searchFields.length > 0) {
        searchParams.search_fields = searchFields.join(',')
      }

      // Add sorting if provided
      if (sortBy) {
        searchParams.sort_by = sortBy
        searchParams.sort_order = sortOrder
      }

      console.log('🔍 Performing server search:', searchParams)

      const response = await searchFunction(term, searchParams)

      if (response.success) {
        results.value = response.data || []
        total.value = response.total || 0
        currentPage.value = page
      } else {
        throw new Error(response.message || 'Search failed')
      }
    } catch (err) {
      console.error('Search error:', err)
      error.value = err.message || 'Search failed'
      results.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  // Debounced search function
  const debouncedSearch = debounce((term) => {
    searchTerm.value = term
    performSearch(term, 1)
  }, debounceMs)

  // Search with immediate execution (for programmatic searches)
  const searchImmediate = (term, page = 1) => {
    searchTerm.value = term
    return performSearch(term, page)
  }

  // Handle pagination
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      performSearch(searchTerm.value, page)
    }
  }

  // Handle page size change
  const changePageSize = (newSize) => {
    pageSize.value = newSize
    currentPage.value = 1
    performSearch(searchTerm.value, 1)
  }

  // Reset search state
  const reset = () => {
    searchTerm.value = ''
    clearResults()
    loading.value = false
  }

  return {
    // State
    searchTerm,
    results,
    loading,
    error,
    total,
    currentPage,
    pageSize,

    // Computed
    hasResults,
    hasError,
    isEmpty,
    totalPages,

    // Methods
    search: debouncedSearch,
    searchImmediate,
    goToPage,
    changePageSize,
    clearResults,
    reset
  }
}

/**
 * Specialized hook for university search
 * @param {Object} options - Configuration options
 * @returns {Object} University search state and methods
 */
export async function useUniversitySearch(options = {}) {
  const { communityApi } = await import('@/services/api/communityApi')

  return useServerSearch(communityApi.searchUniversities, {
    searchFields: ['University', 'Country', 'Province'],
    sortBy: 'University',
    sortOrder: 'ASC',
    defaultLimit: 20,
    ...options
  })
}

/**
 * Specialized hook for competition search
 * @param {Object} options - Configuration options
 * @returns {Object} Competition search state and methods
 */
export async function useCompetitionSearch(options = {}) {
  const { competitionsApi } = await import('@/services/api/competitionsApi')

  return useServerSearch(competitionsApi.searchCompetitions, {
    searchFields: ['competition_name', 'route_name'],
    sortBy: 'start_date',
    sortOrder: 'DESC',
    defaultLimit: 20,
    ...options
  })
}

/**
 * Specialized hook for user search
 * @param {Object} options - Configuration options
 * @returns {Object} User search state and methods
 */
export async function useUserSearch(options = {}) {
  const { usersApi } = await import('@/services/api/usersApi')

  return useServerSearch(usersApi.searchUsers, {
    searchFields: ['name', 'email', 'university'],
    sortBy: 'name',
    sortOrder: 'ASC',
    defaultLimit: 20,
    ...options
  })
}
