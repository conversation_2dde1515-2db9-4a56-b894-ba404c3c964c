/**
 * API Services Index
 * Central export for all API service modules
 */

// Import all API services
export { authApi } from './authApi.js'
export { communityApi } from './communityApi.js'
export { campAdminApi } from './campAdminApi.js'
export { analyticsApi } from './analyticsApi.js'
export { competitionsApi } from './competitionsApi.js'
export { usersApi } from './usersApi.js'

// Import base API instance
export { api, API_CONFIG } from '../api.js'

// Re-export utility functions
export {
  buildQueryString,
  handleApiResponse,
  handleApiError,
  retryApiCall,
  createPaginationParams,
  transformMockData,
  isDevelopment,
  isMockApiEnabled,
  logApiCall
} from '../../utils/apiHelpers.js'

// API service configuration
export const API_SERVICES = {
  auth: 'authApi',
  community: 'communityApi',
  campAdmin: 'campAdminApi',
  analytics: 'analyticsApi',
  competitions: 'competitionsApi',
  users: 'usersApi'
}

// Service health check
export const checkServiceHealth = async () => {
  try {
    const { analyticsApi } = await import('./analyticsApi.js')
    const healthResponse = await analyticsApi.getHealthStatus()
    
    return {
      success: true,
      data: {
        analytics: healthResponse.data,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    return {
      success: false,
      error: {
        message: 'Service health check failed',
        details: error.message
      }
    }
  }
}

// Service initialization
export const initializeApiServices = () => {
  console.log('🔧 Initializing API Services...')
  
  const config = {
    baseURL: import.meta.env.VITE_API_BASE_URL,
    mockEnabled: import.meta.env.VITE_MOCK_API !== 'false',
    environment: import.meta.env.VITE_APP_ENV,
    debug: import.meta.env.VITE_DEBUG === 'true'
  }
  
  console.log('📋 API Configuration:', config)
  
  if (config.mockEnabled) {
    console.log('🎭 Mock API is enabled')
  } else {
    console.log('🌐 Real API is enabled')
  }
  
  return config
}
