"""
Pydantic schemas for competition management endpoints.
"""

from pydantic import BaseModel, Field, computed_field
from typing import List, Dict, Any, Optional
from libs.schemas.api.common import PaginatedResponse, StandardResponse
from libs.schemas.api.base import (
    BaseDataPostResponse, 
)
from datetime import datetime


# ——— Migrated from shared/schemas/api/job.py ———


class CompetitionUserRegisterRequest(BaseModel):
    """Request schema for fetching competition users by registration date."""

    register_start_date: Optional[str] = None


class CompetitionUserIdentityRegisterRequest(BaseModel):
    """Request schema for fetching competition users by identity."""

    identity: str
    register_start_date: Optional[str] = None


# ——— Migrated from shared/schemas/api/cs.py ———




# ——— Original domain schemas ———


# Request Schemas
class QualificationCreateRequest(BaseModel):
    """Request schema for creating a new qualification."""

    competition_id: str = Field(..., description="活动 ID，必填")
    credit: int = Field(..., gte=0, description="学分数量，必填, 必须不小于0")
    qualification_type: Optional[int] = Field(default=1, description="得分规则类型，选填, 默认为按照任务提交分数判断")
    qualification_logic: Optional[int] = Field(default=1, description="得分规则逻辑，选填，默认为大于等于某个分数阈值")
    qualification_name: Optional[str] = Field(
        None, description="得分规则名称"
    )
    related_task_id: Optional[str] = Field(None, description="关联的任务 ID，选填")
    score_threshold: Optional[int] = Field(
        0, description="得分规则分数阈值，选填"
    )

class QualificationCreateResponse(BaseDataPostResponse):
    """Response schema for creating a new qualification."""
    
    qualification_id: str = Field(..., description="Qualification ID")
    competition_id: str = Field(..., description="Competition ID")
    competition_name: str = Field(..., description="Competition name")
    route_name: str = Field(..., description="Route name")


class CreditLogCreateResponse(BaseDataPostResponse):
    """Response schema for creating a new credit log."""
    
    id: str = Field(..., description="Credit log ID", alias='record_id')
    
class CreditAwardRequest(BaseModel):
    """Request schema for awarding credits."""

    user_ids: List[str] = Field(..., description="用户 ID 列表，必填")
    competition_id: str = Field(..., description="活动 ID，必填")
    qual_id: str = Field(..., description="发放得分规则的 ID，必填")
    credit: int = Field(..., gte=0, description="发放学分数量，必填, 必须不小于0")
    remark: Optional[str] = Field(None, description="学分发放备注")

class CreditLogRevokeResponse(BaseDataPostResponse):
    """Response schema for revoking credits."""

    id: str = Field(..., description="Credit log ID", alias='record_id')


class CreditRevokeRequest(BaseModel):
    """Request schema for revoking credits."""

    record_id: str = Field(..., description="The ID of the credit record to revoke")


class AdminLogsCreateRequest(BaseModel):
    """Request schema for creating admin logs."""
    
    action_type: int = Field(..., description="Action type")
    action_related_id: Optional[str] = Field(None, description="Action related ID")
    remark: Optional[str] = Field(None, description="Remark") 
    admin_email_id: Optional[str] = Field(None, description="Admin ID")

class AdminLogsCreateResponse(BaseModel):
    """Response schema for creating admin logs."""
    
    record_id: str = Field(..., description="Admin log ID")
    action_type: int = Field(..., description="Action type")
    action_related_id: Optional[str] = Field(None, description="Action related ID")
    remark: Optional[str] = Field(None, description="Remark") 

# Note: Removed duplicate CompetitionUserRegisterRequest and CompetitionUserIdentityRegisterRequest
# as they are now included from the migrated schemas above


# Response Schemas
class RouteResponse(BaseModel):
    """Response schema for route data."""

    id: str = Field(..., description="Route ID",alias = 'route_id')
    name: str = Field(..., description="Route name", alias='route_name')
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for route."""
        return f"赛道: {self.name}"


class CompetitionResponse(BaseModel):
    """Response schema for competition data."""
    id:str = Field(..., description="record id", alias='record_id')
    competition_id: str = Field(..., description="Competition ID")
    competition_name: str = Field(..., description="Competition name")
    route_id: str = Field(..., description="Route ID")
    route_name: str = Field(..., description="Route name")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for competition."""
        return f"{self.competition_name} ({self.route_name})"


class QualificationResponse(BaseModel):
    """Response schema for qualification data."""

    id: str = Field(..., description="Qualification ID",alias = 'qualification_id')
    competition_id: str = Field(..., description="Competition ID")
    qualification_name: Optional[str] = None
    credit: int
    
    # Relationship fields from enhanced service
    competition_name: Optional[str] = Field(None, description="Competition name")
    task_id: Optional[str] = Field(None, description="Related task ID")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for qualification."""
        return self.qualification_name or f"学分规则 {self.id[:8]}"
    
    @computed_field
    @property
    def display_credits(self) -> str:
        """Formatted credit amount."""
        return f"{self.credit} 学分"


class CreditLogResponse(BaseModel):
    """Response schema for credit history."""

    id: str = Field(..., description="Credit log ID",alias = 'record_id')
    user_id: str = Field(..., description="User ID")
    competition_id: str = Field(..., description="Competition ID")
    qual_id: str = Field(..., description="Qualification ID",alias='qualification_id')
    credit: int
    remark: Optional[str] = Field(None, description="学分发放备注")
    batch_id: Optional[str] = Field(None, description="批次ID")
    created_at: Optional[datetime] = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(..., description="更新时间")
    
    # Relationship fields from enhanced service
    user_name: Optional[str] = Field(None, description="User name")
    competition_name: Optional[str] = Field(None, description="Competition name")
    qualification_name: Optional[str] = Field(None, description="Qualification name")
    university: Optional[str] = Field(None, description="University name")
    
    @computed_field
    @property
    def display_credits(self) -> str:
        """Formatted credit amount with sign."""
        sign = "+" if self.credit > 0 else ""
        return f"{sign}{self.credit} 学分"
    
    @computed_field  
    @property
    def display_date(self) -> str:
        """User-friendly date format."""
        if self.created_at:
            return self.created_at.strftime("%Y年%m月%d日 %H:%M")
        return "未知时间"
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for credit log."""
        if self.remark:
            return f"学分记录: {self.remark}"
        return f"学分记录 {self.id[:8]}"
    
    @computed_field
    @property
    def display_full_context(self) -> str:
        """Full context with user and competition info."""
        user_info = self.user_name or f"用户{self.user_id[-4:]}"
        competition_info = self.competition_name or f"活动{self.competition_id}"
        return f"{user_info} - {competition_info} - {self.display_credits}"


class CompetitionUserResponse(BaseModel):
    """Response schema for competition user data."""

    user_id: str = Field(..., description="User ID")
    username: Optional[str] = None
    email: Optional[str] = None
    registration_date: Optional[datetime] = None
    identity: Optional[str] = None
    competition_type: Optional[str] = None
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for user."""
        return self.username or f"用户 {self.user_id[-4:]}"
        
    @computed_field
    @property
    def display_date(self) -> str:
        """User-friendly registration date format."""
        if self.registration_date:
            return self.registration_date.strftime("%Y年%m月%d日")
        return "未知日期"
    
class AdminLogsResponse(BaseModel):
    """Response schema for admin logs."""
    
    record_id: str = Field(..., description="Admin log ID",alias = 'record_id') 
    action_type: int = Field(..., description="Action type")        
    action_related_id: Optional[str] = None
    remark: Optional[str] = None  
    created_at: Optional[datetime] = None
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for admin log."""
        action_names = {
            1: "创建操作",
            2: "更新操作", 
            3: "删除操作",
            4: "查询操作",
            5: "其他操作"
        }
        action_name = action_names.get(self.action_type, f"操作类型{self.action_type}")
        return f"{action_name}: {self.remark}" if self.remark else action_name
    
    @computed_field
    @property
    def display_date(self) -> str:
        """User-friendly date format."""
        if self.created_at:
            return self.created_at.strftime("%Y年%m月%d日 %H:%M:%S")
        return "未知时间"


# List Response Schemas using standardized response patterns
RouteListResponse = PaginatedResponse[RouteResponse]
CompetitionListResponse = PaginatedResponse[CompetitionResponse]
QualificationListResponse = PaginatedResponse[QualificationResponse]
CompetitionUserListResponse = PaginatedResponse[CompetitionUserResponse]
CreditLogListResponse = PaginatedResponse[CreditLogResponse]
AdminLogsListResponse = PaginatedResponse[AdminLogsResponse]

# Detail response schemas using standardized response patterns
RouteDetailResponse = StandardResponse[RouteResponse]
CompetitionDetailResponse = StandardResponse[CompetitionResponse]
QualificationDetailResponse = StandardResponse[QualificationResponse]
CreditLogDetailResponse = StandardResponse[CreditLogResponse]
AdminLogDetailResponse = StandardResponse[AdminLogsResponse]
