"""
Pydantic schemas for competition management endpoints.
"""

from pydantic import BaseModel, Field, computed_field
from typing import Optional, Dict, Any, List
from enum import Enum
from datetime import datetime, date

from libs.schemas.api.common import PaginatedResponse, StandardResponse


# ——— Competition Status and Core Models ———


class CompetitionStatus(str, Enum):
    """Competition status based on start/end dates."""
    UPCOMING = "upcoming"
    ACTIVE = "active" 
    COMPLETED = "completed"


class CompetitionResponse(BaseModel):
    """Response schema for individual competition data."""
    
    record_id: str = Field(..., description="Primary record identifier")
    competition_id: str = Field(..., description="Competition ID")
    competition_name: str = Field(..., description="Competition name")
    start_date: Optional[datetime] = Field(None, description="Competition start date")
    end_date: Optional[datetime] = Field(None, description="Competition end date")
    route_id: str = Field(..., description="Route ID")
    route_name: str = Field(..., description="Route name")
    
    # Computed fields
    participant_count: int = Field(default=0, description="Number of registered participants")
    submission_count: int = Field(default=0, description="Number of submissions")
    
    @computed_field
    @property
    def status(self) -> CompetitionStatus:
        """Compute competition status from fixed dates."""
        # Use fixed dates as specified by user
        fixed_start = date(2025, 7, 1)
        fixed_end = date(2025, 8, 17)
        today = date.today()
        
        if today < fixed_start:
            return CompetitionStatus.UPCOMING
        elif today > fixed_end:
            return CompetitionStatus.COMPLETED
        else:
            return CompetitionStatus.ACTIVE
    
    @computed_field
    @property
    def display_dates(self) -> str:
        """Human-readable date range."""
        # Use fixed dates as specified by user
        return "2025-07-01 至 2025-08-17"


# ——— Response Wrappers ———


class CompetitionListResponse(PaginatedResponse[CompetitionResponse]):
    """Paginated response for competition list endpoints."""
    pass


class CompetitionDetailResponse(StandardResponse[CompetitionResponse]):
    """Response for single competition detail."""
    pass


# ——— Migrated from shared/schemas/api/job.py ———


class CompetitionUserRegisterRequest(BaseModel):
    """Request schema for fetching competition users by registration date."""

    register_start_date: Optional[str] = Field(None, description="注册开始时间")


class CompetitionUserIdentityRegisterRequest(BaseModel):
    """Request schema for fetching competition users by identity."""

    identity: str
    register_start_date: Optional[str] = Field(None, description="注册开始时间")
    

class CompetitionUserProfileType(str, Enum):
    """Types of competition user profile."""
    BASIC = "basic"
    DETAIL = "detail"
    FULL_FORM = "full_form"
    
class CompetitionUserProfileBasic(BaseModel):
    
    user_id: str = Field(..., description="用户ID")
    register_ts: str = Field(alias="register_ts", description="注册时间")
    Phone: Optional[str] = Field(None, alias="Phone", description="手机号")
    Email: Optional[str] = Field(None, alias="Email", description="邮箱")
    competition_id: str = Field(alias="competition_id", description="比赛ID")
    
class CompetitionUserProfileDetail(CompetitionUserProfileBasic):
    competition_type: Optional[str] = Field(..., alias="competition_type", description="比赛类型")
    competition_name: Optional[str] = Field(..., alias="competition_name", description="比赛名称")
    
class CompetitionUserProfileFullForm(CompetitionUserProfileBasic):
    register_form: Dict[str, Any] = Field(alias="RegisterForm", description="注册表单")
    

class CompetitionUserProfileResponse(BaseModel):
    """Response schema for competition user profile."""
    data: List[Dict[str, Any]] = Field(..., description="List of competition user profiles")
    total: int = Field(..., description="Total number of competition user profiles")
    limit: int = Field(..., description="Limit of competition user profiles per page")
    offset: int = Field(..., description="Offset of competition user profiles")