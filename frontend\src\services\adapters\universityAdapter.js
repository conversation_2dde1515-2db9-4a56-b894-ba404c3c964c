/**
 * University Data Adapter
 * Transforms university and major data between API and frontend formats
 */

import { BaseAdapter, AdapterRegistry } from './BaseAdapter.js'

export class UniversityAdapter extends BaseAdapter {
  /**
   * Transform university data from API to frontend format
   * @param {Object} university - University data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed university data
   */
  static transformItem(university, type = 'default') {
    if (!university) return null
    
    const base = super.transformItem(university, type)
    
    switch (type) {
      case 'list':
        return {
          id: university.university_id || university.id,
          name: university.university_name || university.name,
          country: university.country || 'Unknown',
          province: university.province || 'Unknown',
          type: 'university',
          // Add display fields
          displayName: university.university_name || university.name,
          location: `${university.province || 'Unknown'}, ${university.country || 'Unknown'}`,
          ...base
        }
      
      case 'detail':
        return {
          id: university.university_id || university.id,
          name: university.university_name || university.name,
          country: university.country || 'Unknown',
          province: university.province || 'Unknown',
          type: 'university',
          // Enhanced fields for detail view
          displayName: university.university_name || university.name,
          location: `${university.province || 'Unknown'}, ${university.country || 'Unknown'}`,
          logo: university.logo || null,
          description: university.description || '',
          website: university.website || null,
          // Stats (if available)
          stats: university.stats || {
            totalStudents: 0,
            activeStudents: 0,
            totalCompetitions: 0,
            totalSubmissions: 0,
            averageScore: 0
          },
          ...base
        }
      
      case 'dropdown':
        return {
          value: university.university_id || university.id,
          label: university.university_name || university.name,
          country: university.country,
          province: university.province
        }
      
      default:
        return {
          id: university.university_id || university.id,
          name: university.university_name || university.name,
          country: university.country || 'Unknown',
          province: university.province || 'Unknown',
          type: 'university',
          ...base
        }
    }
  }
  
  /**
   * Transform major data from API to frontend format
   * @param {Object} major - Major data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed major data
   */
  static transformMajor(major, type = 'default') {
    if (!major) return null
    
    switch (type) {
      case 'list':
        return {
          id: major.major_id || major.id,
          name: major.major_name || major.name,
          discipline: major.discipline || 'Unknown',
          type: 'major',
          displayName: major.major_name || major.name,
          category: major.discipline || 'Unknown'
        }
      
      case 'dropdown':
        return {
          value: major.major_id || major.id,
          label: major.major_name || major.name,
          discipline: major.discipline
        }
      
      default:
        return {
          id: major.major_id || major.id,
          name: major.major_name || major.name,
          discipline: major.discipline || 'Unknown',
          type: 'major'
        }
    }
  }
  
  /**
   * Transform university list response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transform(response, options = {}) {
    const { type = 'list', isPaginated = true } = options
    
    if (isPaginated) {
      return this.transformPaginated(response, type)
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformItem(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformItem(response.data, type)
    }
  }
  
  /**
   * Transform major list response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transformMajors(response, options = {}) {
    const { type = 'list', isPaginated = true } = options
    
    if (isPaginated) {
      const transformed = this.transformPaginated(response, type)
      return {
        ...transformed,
        data: transformed.data.map(item => this.transformMajor(item, type))
      }
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformMajor(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformMajor(response.data, type)
    }
  }
  
  /**
   * Transform search results
   * @param {Object} response - Search response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed search results
   */
  static transformSearchResults(response, options = {}) {
    const { type = 'list' } = options
    
    return {
      success: response.success,
      data: response.data.map(item => this.transformItem(item, type)),
      total: response.total,
      searchTerm: response.searchTerm
    }
  }
  
  /**
   * Transform university statistics
   * @param {Object} stats - University statistics
   * @returns {Object} Transformed statistics
   */
  static transformStats(stats) {
    if (!stats) return null
    
    return {
      universityId: stats.university_id,
      totalStudents: stats.total_students,
      activeStudents: stats.active_students,
      totalCompetitions: stats.total_competitions,
      totalSubmissions: stats.total_submissions,
      averageScore: stats.average_score,
      participationRate: stats.participation_rate,
      completionRate: stats.completion_rate,
      lastUpdated: stats.last_updated
    }
  }
}

// Register the adapter
AdapterRegistry.register('university', UniversityAdapter)
AdapterRegistry.register('major', UniversityAdapter)

export { UniversityAdapter as universityAdapter }
