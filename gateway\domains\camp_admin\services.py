"""
Competition business logic and data access services.

Contains all business logic for competitions including data access,
validation, and integration with external services.
"""
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s",
    datefmt="%H:%M:%S"
)
# Explicitly bump <PERSON><PERSON><PERSON>'s loggers to DEBUG
for name in ("tortoise", "tortoise.transactions", "tortoise.backends.asyncpg"):
    logging.getLogger(name).setLevel(logging.DEBUG)


from typing import Optional, Dict, Any
from bson import ObjectId
from tortoise import BaseDBAsyncClient, Tortoise
from tortoise.transactions import atomic
from core.database.mongo_db import mongo_manager
from libs.models.tables import (
    Route as TortoiseRoute,
    Competition as TortoiseCompetition,
    Qualification as TortoiseQualification,
    CreditHistory as TortoiseCreditHistory,
    AdminLog as TortoiseAdminLogs,
    UserRegistration as TortoiseUser,
)
from .schemas import ( 
    RouteResponse,
    CompetitionResponse,
    QualificationResponse,
    CreditLogResponse,
    AdminLogsResponse,
    QualificationCreateRequest,
    CreditAwardRequest,
    AdminLogsCreateRequest,
)
from libs.schemas.api.base import BaseDataPostResponse
from libs.schemas.api.common import (
    paginated_response, 
    success_response, 
    SearchParams, 
    SortParams, 
    CampAdminFilterParams,
    PaginationParams,
    QueryBuilder
)
from libs.exceptions import BusinessLogicError, ServiceException, PostgreSQLError, DatabaseConnectionError
from libs.errors import CampAdminDomainErrors, ErrorCode


class CampAdminServices:
    """Business logic services for competition operations."""

    def __init__(self):
        """Initialize competition services."""
        self.mongo_db = mongo_manager

    # ——— Route Management： 赛道管理 ———
    async def get_routes(
        self, 
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None,
        db_session: Optional[BaseDBAsyncClient] = None  
    ) -> Dict[str, Any]:
        """Get list of competition routes with search and sorting."""
        query = TortoiseRoute.all(using_db=db_session)
        
        # check if the pagination is valid
        if limit is not None and offset is not None and limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        # Apply search filters
        searchable_fields = ["route_name", "route_info"]
        search_conditions = QueryBuilder.build_search_filter(search_params, searchable_fields)
        if search_conditions:
            query = query.filter(**search_conditions)
        
        # Get total count for pagination (after search filters)
        total_query = TortoiseRoute.all(using_db=db_session)
        if search_conditions:
            total_query = total_query.filter(**search_conditions)
        total = await total_query.count()
        
        # Apply sorting
        sort_order = QueryBuilder.build_sort_order(sort_params, default_sort="route_name")
        query = query.order_by(*sort_order)
        
        # Apply pagination
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)
        
        # get the routes
        try:
            routes = await query
            route_data = [
                RouteResponse(
                    route_id=r.route_id, 
                    route_name=r.route_name
                ).model_dump(by_alias=True) 
                for r in routes
            ]
            
            return paginated_response(
                data=route_data,
                total=total,
                limit=limit,
                offset=offset,
                message="Routes retrieved successfully with search and sorting"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )


    # ——— Competition Management： 比赛管理 ———
    async def get_competitions(
        self,
        filter_params: Optional[CampAdminFilterParams] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Get list of competitions with enhanced filtering, search, and sorting."""
        
        # Build filter conditions
        filter_conditions = {}
        if filter_params:
            if filter_params.route_id:
                # Check if the route exists
                route_exists = await TortoiseRoute.exists(route_id=filter_params.route_id, using_db=db_session)
                if not route_exists:
                    raise BusinessLogicError(
                        rule="route_not_found",
                        message=f"Route {filter_params.route_id} not found",
                        context={"route_id": filter_params.route_id},
                    )
                filter_conditions["route_id"] = filter_params.route_id
            
            if filter_params.status:
                filter_conditions["status"] = filter_params.status
            
            # Add date range filters
            base_conditions = QueryBuilder.build_filter_conditions(filter_params)
            filter_conditions.update(base_conditions)
        
        # check if the pagination is valid
        if limit is not None and offset is not None and limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        # Apply search filters
        searchable_fields = ["competition_name", "route_name", "competition_id"]
        search_conditions = QueryBuilder.build_search_filter(search_params, searchable_fields)
        
        # Combine filter and search conditions
        all_conditions = {**filter_conditions, **search_conditions}
        
        # get the competitions
        query = TortoiseCompetition.all(using_db=db_session)
        total_query = TortoiseCompetition.all(using_db=db_session)
        
        if all_conditions:
            query = query.filter(**all_conditions)
            total_query = total_query.filter(**all_conditions)
            
        # Get total count for pagination
        total = await total_query.count()
        
        # Apply sorting
        sort_order = QueryBuilder.build_sort_order(sort_params, default_sort="competition_name")
        query = query.order_by(*sort_order)
        
        # Apply pagination
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)
        
        try:
            # return the competitions
            competitions = await query
            competition_data = [
                CompetitionResponse(
                    record_id=c.record_id,
                    competition_id=c.competition_id,
                    competition_name=c.competition_name,
                    route_id=c.route_id,
                    route_name=c.route_name,
                ).model_dump(by_alias=True)
                for c in competitions
            ]
            
            return paginated_response(
                data=competition_data,
                total=total,
                limit=limit,
                offset=offset,
                message="Competitions retrieved successfully with advanced filtering and search"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    # ——— Qualification Management： 晋级逻辑管理 ———
    async def get_qualifications(
        self,
        filter_params: Optional[CampAdminFilterParams] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Get qualifications with enhanced filtering, search, and sorting."""
        
        # Build filter conditions
        filter_conditions = {}
        if filter_params:
            if filter_params.competition_id:
                # Check if the competition exists
                competition_exists = await TortoiseCompetition.exists(
                    competition_id=filter_params.competition_id,
                    using_db=db_session
                )
                if not competition_exists:
                    raise BusinessLogicError(
                        rule="competition_not_found",
                        message=f"Competition {filter_params.competition_id} not found",
                        context={"competition_id": filter_params.competition_id},
                    )
                filter_conditions["competition_id"] = filter_params.competition_id
            
            if filter_params.qualification_id:
                filter_conditions["qualification_id"] = filter_params.qualification_id
            
            # Add base filter conditions
            base_conditions = QueryBuilder.build_filter_conditions(filter_params)
            filter_conditions.update(base_conditions)
                
        if limit is not None and offset is not None and limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )

        # Apply search filters
        searchable_fields = ["qualification_name", "competition_name", "competition_id"]
        search_conditions = QueryBuilder.build_search_filter(search_params, searchable_fields)
        
        # Combine filter and search conditions
        all_conditions = {**filter_conditions, **search_conditions}

        # get the qualifications
        query = TortoiseQualification.all(using_db=db_session)
        total_query = TortoiseQualification.all(using_db=db_session)
        
        if all_conditions:
            query = query.filter(**all_conditions)
            total_query = total_query.filter(**all_conditions)
            
        # Get total count for pagination
        total = await total_query.count()
        
        # Apply sorting
        sort_order = QueryBuilder.build_sort_order(sort_params, default_sort="qualification_name")
        query = query.order_by(*sort_order)
        
        # Apply pagination
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)

        try:
            # return the qualifications
            qualifications = await query
            qualification_data = []
            
            for q in qualifications:
                # Load relationship data including competition and route names
                qualification_item = QualificationResponse(
                    qualification_id=q.qualification_id,
                    qualification_name=q.qualification_name,
                    credit=q.credit,
                    competition_id=q.competition_id,
                ).model_dump(by_alias=True)
                
                # Add relationship data from denormalized fields
                if hasattr(q, 'competition_name') and q.competition_name:
                    qualification_item['competition_name'] = q.competition_name
                if hasattr(q, 'task_id') and q.task_id:
                    qualification_item['task_id'] = q.task_id
                    
                qualification_data.append(qualification_item)
            
            return paginated_response(
                data=qualification_data,
                total=total,
                limit=limit,
                offset=offset,
                message="Qualifications retrieved successfully with enhanced search and filtering"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    async def create_qualification(
        self,
        qualification_data: QualificationCreateRequest,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Create a new qualification."""

        qualification_id = str(ObjectId())
        # check if the qualification already exists
        qualification_exists = await TortoiseQualification.exists(
            competition_id=qualification_data.competition_id,
            qualification_name=qualification_data.qualification_name,
            related_task_id=qualification_data.related_task_id,
            # using_db=db_session
        )
        if qualification_exists:
            raise BusinessLogicError(
                rule="qualification_already_exists",
                message=f"Qualification for competition {qualification_data.competition_id} already exists",
                context={"competition_id": qualification_data.competition_id},
            )
        try:
            qualification = await TortoiseQualification.create(
                qualification_id=qualification_id,
                competition_id=qualification_data.competition_id,
                credit=qualification_data.credit,
                qualification_name=qualification_data.qualification_name,
                related_task_id=qualification_data.related_task_id,
                score_threshold=qualification_data.score_threshold,
                using_db=db_session
            )

            return success_response(
                data={"qualification_id": qualification_id},
                message=f"Qualification '{qualification_data.qualification_name}' created successfully"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )   

    # ——— Credit Management： 学分管理 ———
    async def get_credit_logs(
        self,
        filter_params: Optional[CampAdminFilterParams] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Get credit transaction history with enhanced filtering, search, and sorting."""
        
        # Build filter conditions
        filter_conditions = {"deleted": False}
        if filter_params:
            if filter_params.competition_id:
                filter_conditions["competition_id"] = filter_params.competition_id
            if filter_params.user_id:
                filter_conditions["user_id"] = filter_params.user_id
            
            # Add base filter conditions (date range, etc.)
            base_conditions = QueryBuilder.build_filter_conditions(filter_params)
            filter_conditions.update(base_conditions)
        
        # check if the pagination is valid
        if limit is not None and offset is not None and limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        # Apply search filters
        searchable_fields = ["user_name", "competition_name", "qualification_name", "remark", "university"]
        search_conditions = QueryBuilder.build_search_filter(search_params, searchable_fields)
        
        # Combine filter and search conditions
        all_conditions = {**filter_conditions, **search_conditions}
        
        # get the credit logs
        query = TortoiseCreditHistory.filter(**all_conditions, using_db=db_session)
        total_query = TortoiseCreditHistory.filter(**all_conditions, using_db=db_session)
            
        # Get total count for pagination
        total = await total_query.count()
        
        # Apply sorting
        sort_order = QueryBuilder.build_sort_order(sort_params, default_sort="created_at")
        query = query.order_by(*sort_order)
        
        # Apply pagination
        if offset is not None and offset >= 0:
            query = query.offset(offset)        
        if limit is not None and limit > 0:
            query = query.limit(limit)

        try:
            credit_logs = await query
            credit_log_data = []
            
            for c in credit_logs:
                # Load relationship data - user and competition names from denormalized fields
                credit_log_item = CreditLogResponse(
                    record_id=c.record_id,
                    user_id=c.user_id,
                    competition_id=c.competition_id,
                    credit=c.credit,
                    remark=c.remark,
                    batch_id=c.batch_id,
                    qualification_id=c.qualification_id,
                    created_at=c.created_at,
                    updated_at=c.updated_at,
                ).model_dump(by_alias=True)
                
                # Add relationship data from mixins if available
                if hasattr(c, 'user_name') and c.user_name:
                    credit_log_item['user_name'] = c.user_name
                if hasattr(c, 'competition_name') and c.competition_name:
                    credit_log_item['competition_name'] = c.competition_name
                if hasattr(c, 'qualification_name') and c.qualification_name:
                    credit_log_item['qualification_name'] = c.qualification_name
                if hasattr(c, 'university') and c.university:
                    credit_log_item['university'] = c.university
                    
                credit_log_data.append(credit_log_item)
            
            return paginated_response(
                data=credit_log_data,
                total=total,
                limit=limit,
                offset=offset,
                message="Credit history retrieved successfully with enhanced search and filtering"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    async def award_credits(
        self,
        credit_log_data: CreditAwardRequest,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Award credits to a user."""
        
        user_ids = credit_log_data.user_ids or []
        competition_id = credit_log_data.competition_id
        credit = credit_log_data.credit
        qualification_id = credit_log_data.qual_id
        remark = credit_log_data.remark
        batch_id = str(ObjectId())
        
        # check if the qualification exists
        qualification_exists = await TortoiseQualification.exists(
            qualification_id=qualification_id,
            competition_id=competition_id,
            using_db=db_session
        )
        if not qualification_exists:
            raise BusinessLogicError(
                rule="qualification_not_found",
                message=f"Qualification {qualification_id} not found",
                context={"qualification_id": qualification_id},
            )
        
        bulk_records = []
        for user_id in user_ids:
            # check if the user exists
            user_exists = await TortoiseUser.exists(
                user_id=user_id,
                using_db=db_session
            )
            if not user_exists:
                raise BusinessLogicError(
                    rule="user_not_found",
                    message=f"User {user_id} not found",
                    context={"user_id": user_id},
                )
                
            # check if user in the competition
            user_in_competition = await TortoiseUser.exists(
                competition_id=competition_id,
                user_id=user_id,
                using_db=db_session
            )
            if not user_in_competition:
                raise BusinessLogicError(
                    rule="user_not_in_competition",
                    message=f"User {user_id} not in competition {competition_id}",
                    context={"user_id": user_id, "competition_id": competition_id},
                )

            # check if the credit log already exists
            credit_log_exists = await TortoiseCreditHistory.exists(
                user_id=user_id,
                competition_id=competition_id,
                remark=remark,
                using_db=db_session
            )  
        
            if credit_log_exists:
                raise BusinessLogicError(
                    rule="credit_log_already_exists",
                    message=f"Credit log for user {user_id} and competition {competition_id} already exists",
                    context={"user_id": user_id, "competition_id": competition_id},
                )
            record_id = str(ObjectId())
            bulk_records.append(
                TortoiseCreditHistory(
                    record_id=record_id,
                    user_id=user_id,
                    competition_id=competition_id,
                    credit=credit,
                    batch_id=batch_id, 
                    qualification_id=qualification_id,
                    remark=remark,
                    using_db=db_session
                )
            )

        try:
            await TortoiseCreditHistory.bulk_create(
                bulk_records,
                using_db=db_session
            )
            
            return success_response(
                data={
                    "batch_id": batch_id,
                    "users_count": len(user_ids),
                    "credit_amount": credit
                },
                message=f"Credits awarded successfully to {len(user_ids)} users"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )
    
    async def revoke_credits(
        self, 
        record_id: str, 
        reason: str, 
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Revoke a credit record."""
        # check if the credit log exists
        credit_log_exists = await TortoiseCreditHistory.exists(
            record_id=record_id,
            deleted=False,
            using_db=db_session
        )
        if not credit_log_exists:
            raise BusinessLogicError(
                rule="credit_log_not_found",
                message=f"Credit log {record_id} not found",
                context={"record_id": record_id},
            )

        await TortoiseCreditHistory.filter(
            record_id=record_id,
            using_db=db_session
        ).update(
            deleted=True,
            remark=reason
        )
        
        return success_response(
            data={"record_id": record_id},
            message="Credit record revoked successfully"
        )

    # ——— Admin Logs Management： 管理员日志管理 ———
    async def get_admin_logs(
        self,
        filter_params: Optional[CampAdminFilterParams] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> Dict[str, Any]:
        """Get admin operation logs with enhanced filtering, search, and sorting."""
        
        # Build filter conditions
        filter_conditions = {}
        if filter_params:
            if filter_params.operation:
                filter_conditions["operation"] = filter_params.operation
            if filter_params.user_id:
                filter_conditions["user_id"] = filter_params.user_id
            
            # Add base filter conditions
            base_conditions = QueryBuilder.build_filter_conditions(filter_params)
            filter_conditions.update(base_conditions)
        
        # check if the pagination is valid
        if limit is not None and offset is not None and limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        # Apply search filters
        searchable_fields = ["operation", "remark", "user_id"]
        search_conditions = QueryBuilder.build_search_filter(search_params, searchable_fields)
        
        # Combine filter and search conditions
        all_conditions = {**filter_conditions, **search_conditions}
        
        # get the admin logs
        query = TortoiseAdminLogs.all(using_db=db_session)
        total_query = TortoiseAdminLogs.all(using_db=db_session)
        
        if all_conditions:
            query = query.filter(**all_conditions)
            total_query = total_query.filter(**all_conditions)
            
        # Get total count for pagination
        total = await total_query.count()
        
        # Apply sorting
        sort_order = QueryBuilder.build_sort_order(sort_params, default_sort="created_at")
        query = query.order_by(*sort_order)
        
        # Apply pagination
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)

        try:
            admin_logs = await query
            admin_log_data = [
                AdminLogsResponse(
                    record_id=a.record_id,
                    operation=a.operation,
                    remark=a.remark,
                    user_id=a.user_id,
                    created_at=a.created_at,
                    updated_at=a.updated_at,
                ).model_dump(by_alias=True)
                for a in admin_logs
            ]
            
            return paginated_response(
                data=admin_log_data,
                total=total,
                limit=limit,
                offset=offset,
                message="Admin logs retrieved successfully with enhanced search and filtering"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    # ——— Admin Logs Management： 管理员日志管理 ———
    async def create_admin_log(
        self,
        log_data: AdminLogsCreateRequest,
        db_session: Optional[BaseDBAsyncClient] = None,
    ) -> Dict[str, Any]:
        """Create a new admin log."""
        record_id = str(ObjectId())
        
        try:
            await TortoiseAdminLogs.create(
                record_id=record_id,
                action_type=log_data.action_type,
                action_related_id=log_data.action_related_id,
                remark=log_data.remark,
                admin_email_id=log_data.admin_email_id,
                using_db=db_session
            )
            
            return success_response(
                data={"record_id": record_id},
                message=f"Admin log created successfully: {log_data.remark or 'No description provided'}"
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

# Global services instance
camp_admin_services = CampAdminServices()
