"""
Common API schemas and response utilities.

This module provides standardized schemas and response utilities
for consistent API responses across all domains.
"""

from typing import Optional, List, Any, Dict, Generic, TypeVar, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

# Generic type for data payload
T = TypeVar('T')


# ——— Server-Side Query Parameter Schemas ———

class SortOrder(str, Enum):
    """Standardized sort order options."""
    ASC = "asc"
    DESC = "desc"


class SearchParams(BaseModel):
    """Standardized search parameters."""
    
    query: Optional[str] = Field(None, description="Search query string")
    search_fields: Optional[List[str]] = Field(None, description="Fields to search in")
    case_sensitive: bool = Field(False, description="Whether search is case sensitive")


class SortParams(BaseModel):
    """Standardized sorting parameters."""
    
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: SortOrder = Field(SortOrder.ASC, description="Sort order (asc/desc)")


class FilterParams(BaseModel):
    """Base filtering parameters - to be extended by domain-specific filters."""
    
    start_date: Optional[datetime] = Field(None, description="Filter records after this date")
    end_date: Optional[datetime] = Field(None, description="Filter records before this date")
    status: Optional[str] = Field(None, description="Filter by status")
    is_active: Optional[bool] = Field(None, description="Filter by active status")


class PaginationParams(BaseModel):
    """Standardized pagination parameters."""
    
    limit: Optional[int] = Field(50, ge=1, le=1000, description="Number of records per page")
    offset: Optional[int] = Field(0, ge=0, description="Number of records to skip")
    page: Optional[int] = Field(None, ge=1, description="Page number (alternative to offset)")
    
    def get_offset(self) -> int:
        """Calculate offset from page number if provided."""
        if self.page is not None and self.limit is not None:
            return (self.page - 1) * self.limit
        return self.offset or 0


class StandardQueryParams(BaseModel):
    """Combined standard query parameters for list endpoints."""
    
    search: Optional[SearchParams] = Field(None, description="Search parameters")
    filters: Optional[FilterParams] = Field(None, description="Filter parameters")
    sort: Optional[SortParams] = Field(None, description="Sort parameters")
    pagination: PaginationParams = Field(default_factory=PaginationParams, description="Pagination parameters")


# ——— Domain-Specific Filter Extensions ———

class CampAdminFilterParams(FilterParams):
    """Camp admin specific filtering parameters."""
    
    route_id: Optional[str] = Field(None, description="Filter by route ID")
    competition_id: Optional[str] = Field(None, description="Filter by competition ID")
    user_id: Optional[str] = Field(None, description="Filter by user ID")
    qualification_id: Optional[str] = Field(None, description="Filter by qualification ID")
    operation: Optional[str] = Field(None, description="Filter by operation type")


class CommunityFilterParams(FilterParams):
    """Community specific filtering parameters."""
    
    country: Optional[str] = Field(None, description="Filter by country")
    province: Optional[str] = Field(None, description="Filter by province")
    university: Optional[str] = Field(None, description="Filter by university")
    major: Optional[str] = Field(None, description="Filter by major")
    discipline: Optional[str] = Field(None, description="Filter by discipline")


class AnalyticsFilterParams(FilterParams):
    """Analytics specific filtering parameters."""
    
    category: Optional[str] = Field(None, description="Filter by statistics category")
    route_id: Optional[str] = Field(None, description="Filter by route ID")
    top_n: Optional[int] = Field(None, ge=1, le=1000, description="Limit to top N results")
    min_score: Optional[float] = Field(None, description="Minimum score threshold")
    max_score: Optional[float] = Field(None, description="Maximum score threshold")


class CompetitionsFilterParams(FilterParams):
    """Competitions specific filtering parameters."""
    
    route_id: Optional[str] = Field(None, description="Filter by route ID")
    competition_type: Optional[str] = Field(None, description="Filter by competition type")
    has_participants: Optional[bool] = Field(None, description="Filter by participation status")
    min_participants: Optional[int] = Field(None, ge=0, description="Minimum participant count")
    max_participants: Optional[int] = Field(None, ge=0, description="Maximum participant count")


# ——— Query Builder Utilities ———

class QueryBuilder:
    """Utility class for building database queries with search, filter, and sort."""
    
    @staticmethod
    def build_search_filter(search_params: Optional[SearchParams], searchable_fields: List[str]) -> Dict[str, Any]:
        """Build search filter conditions for Tortoise ORM."""
        if not search_params or not search_params.query:
            return {}
        
        search_conditions = {}
        query = search_params.query.strip()
        
        if not query:
            return {}
        
        # Use specified search fields or default to all searchable fields
        fields_to_search = search_params.search_fields or searchable_fields
        
        # Build OR conditions for multiple fields
        or_conditions = []
        for field in fields_to_search:
            if search_params.case_sensitive:
                or_conditions.append({f"{field}__contains": query})
            else:
                or_conditions.append({f"{field}__icontains": query})
        
        if or_conditions:
            search_conditions["__or"] = or_conditions
        
        return search_conditions
    
    @staticmethod
    def build_filter_conditions(filter_params: Optional[FilterParams]) -> Dict[str, Any]:
        """Build filter conditions for Tortoise ORM."""
        if not filter_params:
            return {}
        
        conditions = {}
        
        # Date range filters
        if filter_params.start_date:
            conditions["created_at__gte"] = filter_params.start_date
        if filter_params.end_date:
            conditions["created_at__lte"] = filter_params.end_date
        
        # Status filters
        if filter_params.status is not None:
            conditions["status"] = filter_params.status
        if filter_params.is_active is not None:
            conditions["is_active"] = filter_params.is_active
        
        return conditions
    
    @staticmethod
    def build_sort_order(sort_params: Optional[SortParams], default_sort: str = "created_at") -> List[str]:
        """Build sort order for Tortoise ORM."""
        if not sort_params or not sort_params.sort_by:
            return [f"-{default_sort}"]  # Default to descending by creation date
        
        sort_field = sort_params.sort_by
        if sort_params.sort_order == SortOrder.DESC:
            sort_field = f"-{sort_field}"
        
        return [sort_field]


# For GET /tags
# No request body, query param 'category'


# For POST /tags/update
class UserTagUpdateRequest(BaseModel):
    tag_id: str
    users: List[str] = Field(default_factory=list)


# For POST /tags/shence
class ShenceTagsCreateRequest(BaseModel):
    tag_id: Optional[str] = None
    tag_name: Optional[str] = None
    data: List[str] = Field(default_factory=list)  # User IDs
    dir_id: Optional[int] = 0


# For POST /user_id
class UserIdQueryRequest(BaseModel):
    ids: List[str] = Field(default_factory=list)


# For POST /tags/<tag_id>/query
class TagUserQueryRequest(BaseModel):
    fields: List[str] = Field(default_factory=list)


class PaginationMeta(BaseModel):
    """Standardized pagination metadata."""
    
    total: int = Field(..., description="Total number of records")
    limit: int = Field(..., description="Number of records per page")
    offset: int = Field(..., description="Number of records skipped")
    page: int = Field(..., description="Current page number (calculated from offset/limit)")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_prev: bool = Field(..., description="Whether there are previous pages")
    
    @classmethod
    def create(cls, total: int, limit: int, offset: int) -> "PaginationMeta":
        """Create pagination metadata from basic parameters."""
        page = (offset // limit) + 1 if limit > 0 else 1
        total_pages = (total + limit - 1) // limit if limit > 0 else 1
        
        return cls(
            total=total,
            limit=limit,
            offset=offset,
            page=page,
            total_pages=total_pages,
            has_next=offset + limit < total,
            has_prev=offset > 0
        )


class StandardResponse(BaseModel, Generic[T]):
    """Standardized API response wrapper."""
    
    success: bool = Field(True, description="Operation success status")
    message: str = Field("success", description="Response message")
    data: Optional[T] = Field(None, description="Response data payload")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class PaginatedResponse(BaseModel, Generic[T]):
    """Standardized paginated response."""
    
    success: bool = Field(True, description="Operation success status")
    message: str = Field("success", description="Response message")
    data: List[T] = Field(default_factory=list, description="Response data items")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class ErrorResponse(BaseModel):
    """Standardized error response."""
    
    success: bool = Field(False, description="Operation success status")
    message: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Specific error code")
    details: Optional[dict] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


# Legacy support - keeping existing schemas for backward compatibility
class CommonResponse(BaseModel):
    message: str = "success"
    data: Any


class EventTracksResponse(BaseModel):
    """Event tracks response schema."""
    
    event_id: str = Field(..., description="Event identifier")
    track_name: str = Field(..., description="Track name")
    track_info: Optional[str] = Field(None, description="Track information")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


# Response utility functions
def success_response(data: Any = None, message: str = "success") -> StandardResponse:
    """Create a successful response."""
    return StandardResponse(success=True, message=message, data=data)


def error_response(message: str, error_code: str = None, details: dict = None) -> ErrorResponse:
    """Create an error response."""
    return ErrorResponse(
        success=False,
        message=message,
        error_code=error_code,
        details=details
    )


def paginated_response(
    data: List[Any],
    total: int,
    limit: int,
    offset: int,
    message: str = "success"
) -> PaginatedResponse:
    """Create a paginated response with metadata."""
    pagination = PaginationMeta.create(total=total, limit=limit, offset=offset)
    return PaginatedResponse(
        success=True,
        message=message,
        data=data,
        pagination=pagination
    )
