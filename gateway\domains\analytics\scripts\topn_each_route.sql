WITH UserCredits AS (
    SELECT
        u.user_id,
        u.user_name,
        u.university,
        c.route_id,
        c.route_name,
        SUM(u.credit) AS total_credits
    FROM
        users_qualified u
    JOIN
        competitions c ON u.competition_id = c.competition_id
    WHERE
        c.route_name != '其他'
    GROUP BY
        u.user_id, u.user_name, u.university, c.route_id, c.route_name
),
RankedUsers AS (
    SELECT
        uc.*,
        ROW_NUMBER() OVER (PARTITION BY uc.route_id ORDER BY uc.total_credits DESC, uc.user_name ASC) AS row_num
    FROM
        UserCredits uc
),
TopRankedUsers AS (
    SELECT
        route_id,
        route_name,
        total_credits,
        user_name,
        row_num,
        RIGHT(user_id, 4) AS user_id
    FROM
        RankedUsers
    WHERE
        row_num <= {{top_n}}
)
SELECT
    route_name,
    string_agg(user_name, ',' ORDER BY row_num) AS user_names,
    string_agg(cast(total_credits as varchar), ',' ORDER BY row_num) AS total_credits,
    string_agg(user_id, ',' ORDER BY row_num) AS user_ids
FROM
    TopRankedUsers
GROUP BY
    route_id,
    route_name
ORDER BY
    route_name ASC; 