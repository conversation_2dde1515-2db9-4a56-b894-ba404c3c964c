<template>
  <div class="user-search-test">
    <div class="page-header">
      <h1>用户搜索测试页面</h1>
      <p>测试服务器端用户搜索功能</p>
    </div>

    <!-- User Search Component -->
    <UserSearch />

    <!-- API Test Section -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>API 测试</span>
      </template>

      <el-row :gutter="16">
        <el-col :span="12">
          <h3>直接 API 调用测试</h3>
          <el-form :model="apiTestForm" label-width="100px">
            <el-form-item label="搜索词">
              <el-input v-model="apiTestForm.searchTerm" placeholder="输入搜索词" />
            </el-form-item>
            
            <el-form-item label="搜索类型">
              <el-select v-model="apiTestForm.searchType" placeholder="选择搜索类型">
                <el-option label="姓名" value="name" />
                <el-option label="邮箱" value="email" />
                <el-option label="手机号" value="phone" />
                <el-option label="用户ID" value="user_id" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="档案类型">
              <el-select v-model="apiTestForm.profileType" placeholder="选择档案类型">
                <el-option label="基础" value="basic" />
                <el-option label="动态" value="dynamic" />
                <el-option label="学生" value="student" />
                <el-option label="详细" value="rich" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="testDirectApi" :loading="apiTestLoading">
                测试 API 调用
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>

        <el-col :span="12">
          <h3>API 响应</h3>
          <div class="api-response">
            <el-input
              v-model="apiResponse"
              type="textarea"
              :rows="15"
              readonly
              placeholder="API 响应将显示在这里..."
            />
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- Competition Search Test -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>竞赛搜索测试</span>
      </template>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-input
            v-model="competitionSearchTerm"
            placeholder="搜索竞赛..."
            clearable
            @input="testCompetitionSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        
        <el-col :span="16">
          <div v-if="competitionSearchLoading">搜索中...</div>
          <div v-else-if="competitionResults.length > 0">
            找到 {{ competitionResults.length }} 个竞赛
          </div>
          <div v-else-if="competitionSearchTerm">
            未找到匹配的竞赛
          </div>
        </el-col>
      </el-row>

      <div v-if="competitionResults.length > 0" style="margin-top: 16px;">
        <el-table :data="competitionResults.slice(0, 5)" style="width: 100%">
          <el-table-column prop="title" label="竞赛名称" />
          <el-table-column prop="status" label="状态" width="100" />
          <el-table-column prop="startDate" label="开始日期" width="120" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import UserSearch from '@/components/UserSearch.vue'
import { usersApi } from '@/services/api/usersApi'
import { useCompetitionSearch } from '@/composables/useServerSearch'
import { debounce } from 'lodash-es'

// API Test Form
const apiTestForm = reactive({
  searchTerm: '',
  searchType: 'name',
  profileType: 'basic'
})

const apiTestLoading = ref(false)
const apiResponse = ref('')

// Test direct API call
const testDirectApi = async () => {
  if (!apiTestForm.searchTerm.trim()) {
    ElMessage.warning('请输入搜索词')
    return
  }

  apiTestLoading.value = true
  apiResponse.value = ''

  try {
    const response = await usersApi.searchUsers(apiTestForm.searchTerm, {
      search_type: apiTestForm.searchType,
      profile_type: apiTestForm.profileType,
      limit: 10,
      offset: 0
    })

    apiResponse.value = JSON.stringify(response, null, 2)
    
    if (response.success) {
      ElMessage.success(`搜索成功，找到 ${response.data.length} 个用户`)
    } else {
      ElMessage.error('搜索失败')
    }
  } catch (error) {
    console.error('API test error:', error)
    apiResponse.value = JSON.stringify({
      error: true,
      message: error.message,
      details: error
    }, null, 2)
    ElMessage.error(`API 调用失败: ${error.message}`)
  } finally {
    apiTestLoading.value = false
  }
}

// Competition search test
const competitionSearchTerm = ref('')
const competitionSearchLoading = ref(false)
const competitionResults = ref([])

// Initialize competition search
const competitionSearch = await useCompetitionSearch({
  searchFields: ['competition_name', 'route_name'],
  sortBy: 'start_date',
  sortOrder: 'DESC',
  defaultLimit: 20
})

// Debounced competition search
const testCompetitionSearch = debounce(async () => {
  if (!competitionSearchTerm.value.trim()) {
    competitionResults.value = []
    return
  }

  competitionSearchLoading.value = true
  
  try {
    await competitionSearch.searchImmediate(competitionSearchTerm.value, 1)
    competitionResults.value = competitionSearch.results.value
  } catch (error) {
    console.error('Competition search error:', error)
    ElMessage.error(`竞赛搜索失败: ${error.message}`)
  } finally {
    competitionSearchLoading.value = false
  }
}, 500)
</script>

<style lang="scss" scoped>
.user-search-test {
  padding: 20px;

  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
    }
    
    p {
      color: #606266;
      font-size: 16px;
    }
  }

  .api-response {
    .el-textarea {
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
  }
}
</style>
