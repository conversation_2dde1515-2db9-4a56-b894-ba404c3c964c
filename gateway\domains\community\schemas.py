"""
Community domain schemas for university and major management.

Request and response models for:
- University listing and management
- Major categorization
- Educational data services
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, computed_field

# ——— Import shared base schemas ———
from libs.schemas.api.common import PaginatedResponse, StandardResponse


# ——— Response Models ———


class UniversityResponse(BaseModel):
    """Response schema for university data."""
    
    # Use Field aliases to maintain API compatibility with existing frontend expectations
    university: str = Field(..., description="University name", alias="University")
    country: str = Field(..., description="Country", alias="Country") 
    province: str = Field(..., description="Province", alias="Province")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for university."""
        return self.university
    
    @computed_field
    @property
    def display_location(self) -> str:
        """Formatted location string."""
        if self.country == "中国":
            return f"{self.province}"
        else:
            return f"{self.country}, {self.province}"
    
    @computed_field
    @property
    def display_full_name(self) -> str:
        """Full university name with location."""
        return f"{self.university} ({self.display_location})"


class MajorResponse(BaseModel):
    """Response schema for major data."""
    
    # Use Field aliases to maintain API compatibility with existing frontend expectations
    major: str = Field(..., description="Major name", alias="Major")
    discipline: str = Field(..., description="Discipline category", alias="Discipline")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for major."""
        return self.major
    
    @computed_field
    @property
    def display_category(self) -> str:
        """Formatted discipline category."""
        return f"{self.discipline}类"
    
    @computed_field
    @property
    def display_full_name(self) -> str:
        """Full major name with discipline."""
        return f"{self.major} ({self.display_category})"


# ——— Standardized Response Wrappers ———


class UniversityListResponse(PaginatedResponse[UniversityResponse]):
    """Paginated response for university list endpoints."""
    pass


class MajorListResponse(PaginatedResponse[MajorResponse]):
    """Paginated response for major list endpoints."""
    pass


# ——— Detail Response Schemas ———


class UniversityDetailResponse(StandardResponse[UniversityResponse]):
    """Response for single university detail."""
    pass


class MajorDetailResponse(StandardResponse[MajorResponse]):
    """Response for single major detail."""
    pass


# ——— Request Models ———


class UniversityListRequest(BaseModel):
    """Request parameters for university list queries."""
    
    country: Optional[str] = Field(None, description="Filter by country")
    province: Optional[str] = Field(None, description="Filter by province")
    search: Optional[str] = Field(None, description="Search university names")
    limit: int = Field(default=50, ge=1, le=1000, description="Number of results to return")
    offset: int = Field(default=0, ge=0, description="Number of results to skip")


class MajorListRequest(BaseModel):
    """Request parameters for major list queries."""
    
    discipline: Optional[str] = Field(None, description="Filter by discipline category")
    search: Optional[str] = Field(None, description="Search major names")
    limit: int = Field(default=50, ge=1, le=1000, description="Number of results to return") 
    offset: int = Field(default=0, ge=0, description="Number of results to skip")


# Export schemas
__all__ = [
    # Response schemas
    "UniversityResponse",
    "MajorResponse", 
    "UniversityListResponse",
    "MajorListResponse",
    "UniversityDetailResponse",
    "MajorDetailResponse",
    # Request models
    "UniversityListRequest",
    "MajorListRequest",
]
