import { api } from '@/utils/api'
import { handleApiResponse, handleApiError, logApiCall, buildQueryString } from '@/utils/apiHelpers'

const BASE_PATH = '/users'

/**
 * Search users by identifier (name, email, phone, user_id)
 * @param {string} searchTerm - Search term
 * @param {Object} additionalParams - Additional query parameters
 * @param {string} additionalParams.search_type - Search type (name, email, phone, user_id)
 * @param {string} additionalParams.profile_type - Profile type (basic, dynamic, student, rich)
 * @param {number} additionalParams.limit - Number of results (default: 20)
 * @param {number} additionalParams.offset - Offset for pagination (default: 0)
 * @returns {Promise<Object>} Search results response
 */
export const searchUsers = async (searchTerm, additionalParams = {}) => {
  try {
    // Use server-side search if search term is provided
    if (searchTerm && searchTerm.trim()) {
      const searchParams = {
        queries: [searchTerm.trim()],
        search_type: additionalParams.search_type || 'name',
        profile_type: additionalParams.profile_type || 'basic',
        limit: additionalParams.limit || 20,
        offset: additionalParams.offset || 0,
        ...additionalParams
      }
      
      const queryString = buildQueryString(searchParams)
      const url = `${BASE_PATH}/search${queryString}`
      
      logApiCall('GET', url, null, null)
      
      const response = await api.get(url)
      const result = handleApiResponse(response)
      
      logApiCall('GET', url, null, result)
      
      console.log('🔍 Using server-side user search:', searchParams)
      
      return {
        success: true,
        data: result.data || [],
        total: result.total || result.data?.length || 0
      }
    }

    // Return empty results if no search term
    return {
      success: true,
      data: [],
      total: 0
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/search`, { searchTerm, ...additionalParams }, normalizedError)
    console.error('Error searching users:', normalizedError)
    throw normalizedError
  }
}

/**
 * Discover users by various criteria using POST endpoint
 * @param {Object} spec - Match stage specification for user discovery
 * @param {Object} additionalParams - Additional query parameters
 * @param {string} additionalParams.profile_type - Profile type (basic, dynamic, student, rich)
 * @param {number} additionalParams.days - Number of days to search (1-365)
 * @param {number} additionalParams.limit - Number of results
 * @param {number} additionalParams.offset - Offset for pagination
 * @returns {Promise<Object>} Discovery results response
 */
export const discoverUsers = async (spec, additionalParams = {}) => {
  try {
    const queryParams = {
      profile_type: additionalParams.profile_type || 'basic',
      days: additionalParams.days || 365,
      limit: additionalParams.limit || 50,
      offset: additionalParams.offset || 0
    }
    
    const queryString = buildQueryString(queryParams)
    const url = `${BASE_PATH}/discovery${queryString}`
    
    logApiCall('POST', url, spec, null)
    
    const response = await api.post(url, spec)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, spec, result)
    
    return {
      success: true,
      data: result.data || [],
      total: result.total || result.data?.length || 0
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/discovery`, { spec, ...additionalParams }, normalizedError)
    console.error('Error discovering users:', normalizedError)
    throw normalizedError
  }
}

/**
 * Get user profile by ID
 * @param {string} userId - User ID
 * @param {Object} params - Query parameters
 * @param {string} params.profile_type - Profile type (basic, dynamic, student, rich)
 * @returns {Promise<Object>} User profile response
 */
export const getUserProfile = async (userId, params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/${userId}/profile${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/${userId}/profile`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get HeyWhale tags tree
 * @returns {Promise<Object>} Tags tree response
 */
export const getHeyWhaleTagsTree = async () => {
  try {
    const url = `${BASE_PATH}/integrations/heywhale/tags/tree`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/integrations/heywhale/tags/tree`, null, normalizedError)
    throw normalizedError
  }
}

// Export all functions as a service object
export const usersApi = {
  searchUsers,
  discoverUsers,
  getUserProfile,
  getHeyWhaleTagsTree
}
