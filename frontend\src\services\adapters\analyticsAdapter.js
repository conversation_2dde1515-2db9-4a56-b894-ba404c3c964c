/**
 * Analytics Data Adapter
 * Transforms analytics and statistics data between API and frontend formats
 */

import { BaseAdapter, AdapterRegistry } from './BaseAdapter.js'

export class AnalyticsAdapter extends BaseAdapter {
  /**
   * Transform summary statistics from API to frontend format
   * @param {Array} statistics - Statistics array from API
   * @returns {Object} Transformed statistics
   */
  static transformSummaryStatistics(statistics) {
    if (!Array.isArray(statistics)) return {}
    
    const transformed = {}
    const displayStats = []
    
    statistics.forEach(stat => {
      const key = this.normalizeStatisticKey(stat.statistics_name)
      const value = stat.statistics_value
      
      transformed[key] = value
      displayStats.push({
        name: stat.statistics_name,
        value: value,
        displayValue: this.formatStatisticValue(value, stat.statistics_name),
        key: key
      })
    })
    
    return {
      raw: transformed,
      display: displayStats,
      // Common statistics
      totalParticipants: transformed.totalParticipants,
      activeCompetitions: transformed.activeCompetitions,
      totalSubmissions: transformed.totalSubmissions,
      totalCredits: transformed.totalCredits,
      averageScore: transformed.averageScore
    }
  }
  
  /**
   * Transform school rankings from API to frontend format
   * @param {Object} response - School rankings response
   * @returns {Object} Transformed rankings
   */
  static transformSchoolRankings(response) {
    if (!response.data || !response.data.res) {
      return {
        schools: [],
        total: 0,
        page: 1,
        pageSize: 20,
        hasMore: false
      }
    }
    
    const schools = response.data.res.map((school, index) => ({
      rank: (response.data.current_page - 1) * response.data.page_size + index + 1,
      university: school.university,
      universityName: school.university,
      totalCredits: school.total_credits,
      displayCredits: this.formatCredits(school.total_credits),
      displayRank: this.formatRank((response.data.current_page - 1) * response.data.page_size + index + 1)
    }))
    
    return {
      schools,
      total: response.data.total,
      page: response.data.current_page,
      pageSize: response.data.page_size,
      hasMore: (response.data.current_page * response.data.page_size) < response.data.total
    }
  }
  
  /**
   * Transform user rankings from API to frontend format
   * @param {Object} response - User rankings response
   * @returns {Object} Transformed rankings
   */
  static transformUserRankings(response) {
    if (!response.data || !response.data.res) {
      return {
        users: [],
        total: 0,
        page: 1,
        pageSize: 20,
        hasMore: false
      }
    }
    
    const users = response.data.res.map((user, index) => ({
      rank: (response.data.current_page - 1) * response.data.page_size + index + 1,
      userId: user.user_id,
      userName: user.user_name || 'Unknown User',
      totalCredits: user.total_credits,
      universityName: user.university_name,
      majorName: user.major_name,
      displayCredits: this.formatCredits(user.total_credits),
      displayRank: this.formatRank((response.data.current_page - 1) * response.data.page_size + index + 1),
      displayName: user.user_name || 'Unknown User'
    }))
    
    return {
      users,
      total: response.data.total,
      page: response.data.current_page,
      pageSize: response.data.page_size,
      hasMore: (response.data.current_page * response.data.page_size) < response.data.total
    }
  }
  
  /**
   * Transform user rankings by route from API to frontend format
   * @param {Array} routeRankings - Route rankings array
   * @returns {Object} Transformed route rankings
   */
  static transformUserRankingsByRoute(routeRankings) {
    if (!Array.isArray(routeRankings)) return {}
    
    const transformed = {}
    
    routeRankings.forEach(routeData => {
      const routeId = routeData.route_id || routeData.routeId
      const routeName = routeData.route_name || routeData.routeName || `Route ${routeId}`
      
      transformed[routeId] = {
        routeId,
        routeName,
        users: (routeData.users || routeData.rankings || []).map((user, index) => ({
          rank: index + 1,
          userId: user.user_id,
          userName: user.user_name || 'Unknown User',
          totalCredits: user.total_credits,
          displayCredits: this.formatCredits(user.total_credits),
          displayRank: this.formatRank(index + 1),
          displayName: user.user_name || 'Unknown User'
        }))
      }
    })
    
    return transformed
  }
  
  /**
   * Transform top users by route from API to frontend format
   * @param {Array} topUsers - Top users array
   * @returns {Object} Transformed top users
   */
  static transformTopUsersByRoute(topUsers) {
    if (!Array.isArray(topUsers)) return {}
    
    const transformed = {}
    
    topUsers.forEach(routeData => {
      const routeId = routeData.route_id || routeData.routeId
      const routeName = routeData.route_name || routeData.routeName || `Route ${routeId}`
      
      transformed[routeId] = {
        routeId,
        routeName,
        topUsers: (routeData.top_users || routeData.users || []).map((user, index) => ({
          rank: index + 1,
          userId: user.user_id,
          userName: user.user_name || 'Unknown User',
          totalCredits: user.total_credits,
          displayCredits: this.formatCredits(user.total_credits),
          displayRank: this.formatRank(index + 1)
        }))
      }
    })
    
    return transformed
  }
  
  /**
   * Transform event tracks from API to frontend format
   * @param {Object} response - Event tracks response
   * @returns {Object} Transformed event tracks
   */
  static transformEventTracks(response) {
    if (!response.data) {
      return {
        events: [],
        total: 0
      }
    }
    
    const events = (response.data.events || []).map(event => ({
      id: event.id || event.event_id,
      type: event.type || event.event_type,
      name: event.name || event.event_name,
      timestamp: event.timestamp || event.created_at,
      userId: event.user_id,
      data: event.data || event.event_data,
      displayTime: this.formatDateTime(event.timestamp || event.created_at),
      displayType: this.formatEventType(event.type || event.event_type)
    }))
    
    return {
      events,
      total: response.data.total
    }
  }
  
  /**
   * Normalize statistic key for consistent access
   * @param {string} name - Statistic name
   * @returns {string} Normalized key
   */
  static normalizeStatisticKey(name) {
    return name
      .toLowerCase()
      .replace(/\s+/g, '')
      .replace(/[^a-z0-9]/g, '')
      .replace(/^(total|active)/, '')
      .replace(/participants?/, 'participants')
      .replace(/competitions?/, 'competitions')
      .replace(/submissions?/, 'submissions')
      .replace(/credits?/, 'credits')
      .replace(/scores?/, 'score')
  }
  
  /**
   * Format statistic value for display
   * @param {number} value - Statistic value
   * @param {string} name - Statistic name
   * @returns {string} Formatted value
   */
  static formatStatisticValue(value, name) {
    if (name.toLowerCase().includes('rate') || name.toLowerCase().includes('percentage')) {
      return `${(value * 100).toFixed(1)}%`
    }
    
    if (name.toLowerCase().includes('score') || name.toLowerCase().includes('average')) {
      return value.toFixed(2)
    }
    
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    }
    
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    
    return value.toString()
  }
  
  /**
   * Format credits for display
   * @param {number} credits - Credit amount
   * @returns {string} Formatted credits
   */
  static formatCredits(credits) {
    if (!credits) return '0 credits'
    
    if (credits >= 1000) {
      return `${(credits / 1000).toFixed(1)}K credits`
    }
    
    return `${credits} credits`
  }
  
  /**
   * Format rank for display
   * @param {number} rank - Rank number
   * @returns {string} Formatted rank
   */
  static formatRank(rank) {
    if (!rank) return 'Unranked'
    
    const suffix = this.getOrdinalSuffix(rank)
    return `${rank}${suffix}`
  }
  
  /**
   * Get ordinal suffix for numbers
   * @param {number} num - Number
   * @returns {string} Ordinal suffix
   */
  static getOrdinalSuffix(num) {
    const j = num % 10
    const k = num % 100
    
    if (j === 1 && k !== 11) return 'st'
    if (j === 2 && k !== 12) return 'nd'
    if (j === 3 && k !== 13) return 'rd'
    return 'th'
  }
  
  /**
   * Format date and time for display
   * @param {string} dateString - ISO date string
   * @returns {string} Formatted date and time
   */
  static formatDateTime(dateString) {
    if (!dateString) return 'Unknown'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleString()
    } catch (error) {
      return 'Invalid Date'
    }
  }
  
  /**
   * Format event type for display
   * @param {string} type - Event type
   * @returns {string} Formatted event type
   */
  static formatEventType(type) {
    if (!type) return 'Unknown'
    
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }
}

// Register the adapter
AdapterRegistry.register('analytics', AnalyticsAdapter)
AdapterRegistry.register('statistics', AnalyticsAdapter)
AdapterRegistry.register('rankings', AnalyticsAdapter)

export { AnalyticsAdapter as analyticsAdapter }
