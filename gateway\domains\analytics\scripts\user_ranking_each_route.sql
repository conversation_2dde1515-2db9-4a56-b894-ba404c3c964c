WITH UserCredits AS (
    SELECT
        u.user_id,
        u.user_name,
        SUM(u.credit) AS total_credits
    FROM
        users_qualified u
    JOIN
        competitions c ON u.competition_id = c.competition_id
    WHERE
        c.route_id = '{{route_id}}'
    GROUP BY
        u.user_id, u.user_name
),
RankedUser AS (
    SELECT
        uc.*,
        ROW_NUMBER() OVER (ORDER BY total_credits DESC, user_name ASC) AS row_num
    FROM
        UserCredits uc
)
SELECT
    ru.user_name,
    RIGHT(ru.user_id, 4) as user_id,
    ru.total_credits
FROM
    RankedUser ru
WHERE
    ru.row_num <= {{top_num}}
ORDER BY
    ru.row_num ASC; 