# Development API Configuration
VITE_API_BASE_URL=/api/v1

# Mock API Configuration (DISABLED - use real API)
VITE_MOCK_API=false
VITE_MOCK_DELAY=0

# Application Configuration
VITE_APP_TITLE=Community Services Admin (Development)
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# Debug Configuration
VITE_DEBUG=true
VITE_LOG_LEVEL=debug

# Feature Flags
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_COMPETITIONS=true
VITE_FEATURE_CREDITS=true
VITE_FEATURE_SCHOOLS=true
