"""
Analytics Domain Router.

NOTE: 
- this route is designed specifically for events or activities that REQUIRE stats displayed on the frontend.
- this route is NOT designed for data analysis or reporting or dashboarding.

Handles all analytics-related endpoints organized by business function:
- Rankings (school and user rankings)
- Platform statistics
- Event tracking and processing
- ShenCe analytics integration (tag management)

"""

import json
import os
from fastapi import APIRouter, HTTPException, Request, Depends, Body
import logging
import aiofiles
from typing import Optional, Dict, Any
from libs.schemas.api import (
    EventTracksResponse,
)
from .schemas import (
    SchoolRanking,
    SchoolRankingResponse,
    UserRankingEveryRoute,
    UserRankingEveryRouteResponse,
    UserRankingTotalRoute,
    UserRankingTotalRouteResponse,
    SummaryStatisticsResponse,
    AnalyticsHealthResponse,
    TopNByRoute,
    TopNByRouteResponse,
    ShenceTagCreateRequest,
    ShenceFileUploadRequest,
    ShenceTagUpdateRequest,
    ShenceDirectoryResponse,
    ShenceTagMetaResponse,
    ShenceUploadResponse,
    ShenceTagResponse,
)
from libs.auth.permissions import (
    require_ranking_access,
    require_statistics_access,
    require_event_tracking_access,
    require_shence_access_permission,
)
from libs.auth.authentication import log_analytics_access, get_current_user_id
from core.database import get_event_tracking_service
from libs.schemas.api.responses import success, ErrorResponse, SuccessResponse

# Import domain services directly
from .services import AnalyticsServices

logger = logging.getLogger(__name__)

# Create router with prefix for analytics endpoints
router = APIRouter(
    prefix="/analytics",
    tags=["analytics"],
    responses={404: {"description": "Not found"}},
)

# Initialize domain service
analytics_service = AnalyticsServices()

# ——— Health check endpoint ———


@router.get(
    "/health",
    response_model=AnalyticsHealthResponse,
    summary="Analytics service health check",
    description="Check the health status of analytics services",
)
async def analytics_health():
    """Check analytics service health."""
    try:
        event_service = await get_event_tracking_service()

        return AnalyticsHealthResponse(
            status="healthy",
            rankings_available=analytics_service is not None,
            statistics_available=analytics_service is not None,
            event_tracking_available=event_service is not None,
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}", exc_info=True)
        return AnalyticsHealthResponse(
            status="unhealthy",
            rankings_available=False,
            statistics_available=False,
            event_tracking_available=False,
        )


# ——— Camp-Only: Statistics endpoints ———


@router.post(
    "/camp-only/statistics/summary",
    summary="返回夏令营的统计数据",
    description="返回夏令营的核心统计数据",
)
async def get_summary_statistics(
    request: Request,
    # user_id: Optional[str] = Depends(require_statistics_access),
) -> Dict[str, Any]:
    """Get platform summary statistics."""
    try:
        result_data = await analytics_service.get_summary_statistics()
        return result_data

    except Exception as e:
        logger.error(f"Error getting summary statistics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Camp-Only: School ranking endpoints ———


@router.post(
    "/camp-only/rankings/schools",
    summary="返回学校排名",
    description="返回学校排名",
)
async def get_school_rankings(
    request: Request,
    params: SchoolRanking = Body(...),
    # user_id: str = Depends(require_ranking_access),
) -> Dict[str, Any]:
    """Get school rankings by total credits."""
    try:
        result_data = await analytics_service.get_school_ranking(
            page_size=params.page_size, current_page=params.current_page
        )
        return result_data

    except Exception as e:
        logger.error(f"Error getting school rankings: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Camp-Only: User ranking endpoints ———


@router.post(
    "/camp-only/rankings/users/by_route",
    summary="返回用户排名",
    description="返回用户排名",
)
async def get_user_rankings_by_route(
    request: Request,
    params: UserRankingEveryRoute = Body(...),
        # user_id: str = Depends(require_ranking_access),
) -> Dict[str, Any]:
    """Get user rankings for each competition route."""
    try:
        result_data = await analytics_service.get_user_ranking_by_route(
            top_n=params.top_num
        )

        # await log_analytics_access(request, user_id, "user_rankings_by_route")
        return result_data

    except Exception as e:
        logger.error(f"Error getting user rankings by route: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/camp-only/rankings/users/total",
    summary="返回用户总排名",
    description="返回用户总排名",
)
async def get_total_user_rankings(
    request: Request,
    params: UserRankingTotalRoute = Body(...),
    # user_id: str = Depends(require_ranking_access),
) -> Dict[str, Any]:
    """Get user rankings by total credits across all routes."""
    try:
        result_data = await analytics_service.get_user_ranking_total(
            page_size=params.page_size, current_page=params.current_page
        )

        # await log_analytics_access(request, user_id, "total_user_rankings")
        return result_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting total user rankings: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/camp-only/rankings/top_by_route",
    summary="返回每个赛道的top N用户",
    description="返回每个赛道的top N用户",
)
async def get_top_users_by_route(
    request: Request,
    params: TopNByRoute = Body(...),
    # user_id: str = Depends(require_ranking_access),
) -> Dict[str, Any]:
    """Get top N users for each competition route."""
    try:
        result_data = await analytics_service.get_topn_each_route(
            top_num=params.top_num
        )

        # await log_analytics_access(request, user_id, "top_users_by_route")
        return result_data

    except Exception as e:
        logger.error(f"Error getting top users by route: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Event tracking endpoints ———


@router.get(
    "/events/tracks",
    response_model=EventTracksResponse,
    summary="Process and retrieve event tracks",
    description="Read event tracks from file, process them, and return filtered data",
)
async def get_event_tracks(
    request: Request,
    user_id: str = Depends(require_event_tracking_access),
    event_service=Depends(get_event_tracking_service),
):
    """Process and return event tracking data."""
    try:
        await log_analytics_access(request, user_id, "event_tracks")

        # Read event tracks file
        file_path = "logs/event_tracks.jsonl"
        events = []

        if os.path.exists(file_path):
            async with aiofiles.open(file_path, mode="r") as file:
                async for line in file:
                    try:
                        event_data = json.loads(line.strip())
                        events.append(event_data)
                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON line in event tracks: {line}")
                        continue

        # Return event tracks in expected format
        return EventTracksResponse(
            event_id="event_tracks_aggregated",
            track_name="Event Tracks",
            track_info=f"Processed {len(events)} events",
            created_at=events[0].get("timestamp") if events else None,
        )

    except Exception as e:
        logger.error(f"Error getting event tracks: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Tags endpoints ———


@router.get(
    "/tags",
    response_model=SuccessResponse | ErrorResponse,
    summary="Get analytics tags by category",
    description="Get analytics tags filtered by category",
)
async def get_analytics_tags(
    category: str,
    user_id: str = Depends(get_current_user_id),
):
    """Get analytics tags by category."""
    try:
        result_data = await analytics_service.get_tags_by_category(category)
        return success(data=result_data)

    except Exception as e:
        logger.error(f"Error getting analytics tags: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— ShenCe integration endpoints ———


@router.get(
    "/integrations/shence/directories",
    response_model=ShenceDirectoryResponse,
    summary="Get ShenCe directories",
    description="Get ShenCe tag directory metadata for analytics",
)
async def get_shence_directories(
    user_id: str = Depends(require_shence_access_permission),
):
    """Get ShenCe directory metadata."""
    try:
        result_data = await analytics_service.get_shence_dir_meta()

        return ShenceDirectoryResponse(
            directories=result_data.get("directories", []),
            total_count=result_data.get("total_count", 0),
            project=result_data.get("project", "unknown"),
        )

    except Exception as e:
        logger.error(f"Error getting ShenCe directories: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/integrations/shence/tags/{tag_id}/meta",
    response_model=ShenceTagMetaResponse,
    summary="Get ShenCe tag metadata",
    description="Get metadata for a specific ShenCe tag",
)
async def get_shence_tag_metadata(
    tag_id: str,
    user_id: str = Depends(require_shence_access_permission),
):
    """Get ShenCe tag metadata."""
    try:
        result_data = await analytics_service.get_shence_tag_meta(tag_id)

        return ShenceTagMetaResponse(
            tag_id=result_data.get("tag_id", tag_id),
            name=result_data.get("name", ""),
            cname=result_data.get("cname", ""),
            data_type=result_data.get("data_type", "STRING"),
            dir_id=result_data.get("dir_id", ""),
        )

    except Exception as e:
        logger.error(f"Error getting ShenCe tag metadata: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/integrations/shence/files/upload",
    response_model=ShenceUploadResponse,
    summary="Upload file to ShenCe",
    description="Upload CSV data to ShenCe for analytics tag processing",
)
async def upload_shence_file(
    upload_data: ShenceFileUploadRequest = Body(...),
    user_id: str = Depends(require_shence_access_permission),
):
    """Upload file to ShenCe platform."""
    try:
        result_data = await analytics_service.upload_file_to_shence(
            file_name_prefix=upload_data.file_name_prefix,
            csv_data=upload_data.csv_data,
        )

        return ShenceUploadResponse(
            success=result_data.get("success", False),
            file_name=result_data.get("file_name", ""),
            file_size=result_data.get("file_size", 0),
            upload_id=result_data.get("upload_id"),
            message=result_data.get("message", ""),
        )

    except Exception as e:
        logger.error(f"Error uploading file to ShenCe: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/integrations/shence/tags",
    response_model=ShenceTagResponse,
    summary="Create or update ShenCe tags",
    description="Create new ShenCe analytics tags or update existing ones",
)
async def create_shence_tags(
    tag_data: ShenceTagCreateRequest = Body(...),
    user_id: str = Depends(require_shence_access_permission),
):
    """Create or update ShenCe analytics tags."""
    try:
        # First upload the data
        upload_result = await analytics_service.upload_file_to_shence(
            file_name_prefix=tag_data.tag_name,
            csv_data=tag_data.data,
        )

        # Then update the tags
        update_result = await analytics_service.update_shence_tags(
            file_name=upload_result.get("file_name", ""),
            tag_name=tag_data.tag_name,
            tag_id=tag_data.tag_id,
            dir_id=tag_data.dir_id,
            data_type=tag_data.data_type,
        )

        return ShenceTagResponse(
            success=update_result.get("success", False),
            tag_id=update_result.get("tag_id"),
            file_name=upload_result.get("file_name"),
            upload_response=upload_result,
            update_response=update_result.get("update_response"),
            message=update_result.get("message", ""),
        )

    except Exception as e:
        logger.error(f"Error creating ShenCe tags: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/integrations/shence/tags/update",
    response_model=ShenceTagResponse,
    summary="Update ShenCe tags",
    description="Update existing ShenCe analytics tags with new data",
)
async def update_shence_tag(
    update_data: ShenceTagUpdateRequest = Body(...),
    user_id: str = Depends(require_shence_access_permission),
):
    """Update existing ShenCe analytics tags."""
    try:
        result_data = await analytics_service.update_shence_tags(
            file_name=update_data.file_name,
            tag_name=update_data.tag_name,
            tag_id=update_data.tag_id,
            dir_id=update_data.dir_id,
            data_type=update_data.data_type,
            task_name=update_data.task_name,
        )

        return ShenceTagResponse(
            success=result_data.get("success", False),
            tag_id=result_data.get("tag_id"),
            update_response=result_data.get("update_response"),
            message=result_data.get("message", ""),
        )

    except Exception as e:
        logger.error(f"Error updating ShenCe tags: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Phase 2.3 - New Missing Endpoints ———


@router.get(
    "/dashboard",
    summary="Get comprehensive dashboard data",
    description="Get aggregated dashboard data including statistics, rankings, and activity",
)
async def get_dashboard(
    request: Request,
    user_id: Optional[str] = None,
    # user_id: str = Depends(require_statistics_access),
) -> Dict[str, Any]:
    """Get comprehensive dashboard data for analytics overview."""
    try:
        result_data = await analytics_service.get_dashboard_data(user_id=user_id)
        return result_data

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/competitions/{competition_id}/stats",
    summary="Get competition statistics",
    description="Get comprehensive statistics for a specific competition",
)
async def get_competition_statistics(
    competition_id: str,
    request: Request,
    # user_id: str = Depends(require_statistics_access),
) -> Dict[str, Any]:
    """Get detailed statistics for a specific competition."""
    try:
        result_data = await analytics_service.get_competition_stats(competition_id)
        return result_data

    except Exception as e:
        logger.error(f"Error getting competition stats for {competition_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/schools/{university}/stats",
    summary="Get school statistics",
    description="Get comprehensive statistics for a specific school/university",
)
async def get_school_statistics(
    university: str,
    request: Request,
    # user_id: str = Depends(require_statistics_access),
) -> Dict[str, Any]:
    """Get detailed statistics for a specific school."""
    try:
        result_data = await analytics_service.get_school_stats(university)
        return result_data

    except Exception as e:
        logger.error(f"Error getting school stats for {university}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/users/{user_id}/stats",
    summary="Get user statistics",
    description="Get comprehensive statistics for a specific user",
)
async def get_user_statistics(
    user_id: str,
    request: Request,
    # user_id: str = Depends(require_statistics_access),
) -> Dict[str, Any]:
    """Get detailed statistics for a specific user."""
    try:
        result_data = await analytics_service.get_user_stats(user_id)
        return result_data

    except Exception as e:
        logger.error(f"Error getting user stats for {user_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/count/{category}",
    summary="Get count for category",
    description="Get total count for various categories (competitions, users, schools, routes, qualifications)",
)
async def get_category_count(
    category: str,
    request: Request,
    # user_id: str = Depends(require_statistics_access),
) -> Dict[str, Any]:
    """Get count for a specific category."""
    try:
        result_data = await analytics_service.get_count(category)
        return result_data

    except Exception as e:
        logger.error(f"Error getting count for {category}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
