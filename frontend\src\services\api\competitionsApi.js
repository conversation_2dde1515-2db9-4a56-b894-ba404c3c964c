/**
 * Competitions API Service
 * Handles competition data - combines camp-admin and analytics APIs
 */

import { campAdminApi } from './campAdminApi.js'
import { analyticsApi } from './analyticsApi.js'
import { handleApiError, logApiCall } from '../../utils/apiHelpers.js'

/**
 * Get list of competitions with enhanced data and server-side search
 * @param {Object} params - Query parameters
 * @param {string} params.q - Search query
 * @param {string} params.search_fields - Comma-separated search fields (competition_name,route_name)
 * @param {string} params.sort_by - Sort field (start_date, competition_name, etc.)
 * @param {string} params.sort_order - Sort order (ASC/DESC)
 * @param {number} params.limit - Number of competitions to return
 * @param {number} params.offset - Number of competitions to skip
 * @param {string} params.route_id - Filter by route ID
 * @param {string} params.status - Filter by competition status
 * @returns {Promise<Object>} Enhanced competitions list response
 */
export const getCompetitions = async (params = {}) => {
  try {
    logApiCall('GET', '/competitions', params, null)
    
    // Get competitions from camp-admin API
    const competitionsResponse = await campAdminApi.getCompetitions(params)
    console.log(competitionsResponse)
    
    if (!competitionsResponse.success) {
      throw new Error('Failed to fetch competitions')
    }
    
    // Enhance with additional data if needed
    const competitions = competitionsResponse.data.map(competition => ({
      ...competition,
      // Add computed fields
      displayName: competition.competition_name,
      routeName: competition.route_name,
      // Status is now guaranteed by backend
      status: competition.status
    }))
    
    const result = {
      success: true,
      data: competitions,
      total: competitions.length
    }
    
    logApiCall('GET', '/competitions', params, result)
    
    return result
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', '/competitions', params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get competition statistics
 * @param {string} competitionId - Competition ID
 * @returns {Promise<Object>} Competition statistics response
 */
export const getCompetitionStats = async (competitionId) => {
  try {
    logApiCall('GET', `/competitions/${competitionId}/stats`, null, null)
    
    // This would typically call analytics API for competition-specific stats
    // For now, return a placeholder structure based on summary statistics
    const summaryStats = await analyticsApi.getSummaryStatistics()
    
    // Filter or derive competition-specific stats
    const competitionStats = {
      competition_id: competitionId,
      total_participants: 0,
      total_submissions: 0,
      average_score: 0,
      completion_rate: 0,
      // Extract relevant stats from summary if available
      ...summaryStats.data.reduce((acc, stat) => {
        switch (stat.statistics_name) {
          case 'Total Participants':
            acc.total_participants = stat.statistics_value
            break
          case 'Active Competitions':
            // This is global, not competition-specific
            break
          default:
            break
        }
        return acc
      }, {})
    }
    
    const result = {
      success: true,
      data: competitionStats
    }
    
    logApiCall('GET', `/competitions/${competitionId}/stats`, null, result)
    
    return result
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `/competitions/${competitionId}/stats`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Search competitions using server-side search
 * @param {string} searchTerm - Search term
 * @param {Object} additionalParams - Additional query parameters
 * @param {string} additionalParams.search_fields - Fields to search in (default: 'competition_name,route_name')
 * @param {string} additionalParams.sort_by - Sort field (default: 'start_date')
 * @param {string} additionalParams.sort_order - Sort order (default: 'DESC')
 * @param {number} additionalParams.limit - Number of results (default: 20)
 * @param {number} additionalParams.offset - Offset for pagination (default: 0)
 * @returns {Promise<Object>} Search results response
 */
export const searchCompetitions = async (searchTerm, additionalParams = {}) => {
  try {
    // Use server-side search if search term is provided
    if (searchTerm && searchTerm.trim()) {
      const searchParams = {
        q: searchTerm.trim(),
        search_fields: additionalParams.search_fields || 'competition_name,route_name',
        sort_by: additionalParams.sort_by || 'start_date',
        sort_order: additionalParams.sort_order || 'DESC',
        limit: additionalParams.limit || 20,
        offset: additionalParams.offset || 0,
        ...additionalParams
      }

      console.log('🔍 Using server-side competition search:', searchParams)
      return await getCompetitions(searchParams)
    }

    // Return empty results if no search term
    return {
      success: true,
      data: [],
      total: 0
    }
  } catch (error) {
    console.error('Error searching competitions:', error)
    throw error
  }
}

/**
 * Get competitions by route
 * @param {string} routeId - Route ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Competitions filtered by route
 */
export const getCompetitionsByRoute = async (routeId, additionalParams = {}) => {
  return getCompetitions({
    route_id: routeId,
    ...additionalParams
  })
}

/**
 * Get competition routes
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Routes list response
 */
export const getRoutes = async (params = {}) => {
  try {
    logApiCall('GET', '/competitions/routes', params, null)
    
    const routesResponse = await campAdminApi.getRoutes(params)
    
    logApiCall('GET', '/competitions/routes', params, routesResponse)
    
    return routesResponse
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', '/competitions/routes', params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get competition qualifications
 * @param {string} competitionId - Competition ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Qualifications for the competition
 */
export const getCompetitionQualifications = async (competitionId, additionalParams = {}) => {
  try {
    logApiCall('GET', `/competitions/${competitionId}/qualifications`, null, null)
    
    const qualificationsResponse = await campAdminApi.getQualifications({
      competition_id: competitionId,
      ...additionalParams
    })
    
    logApiCall('GET', `/competitions/${competitionId}/qualifications`, null, qualificationsResponse)
    
    return qualificationsResponse
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `/competitions/${competitionId}/qualifications`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Create a new qualification for a competition
 * @param {Object} qualificationData - Qualification data
 * @returns {Promise<Object>} Created qualification response
 */
export const createCompetitionQualification = async (qualificationData) => {
  try {
    logApiCall('POST', '/competitions/qualifications', qualificationData, null)
    
    const response = await campAdminApi.createQualification(qualificationData)
    
    logApiCall('POST', '/competitions/qualifications', qualificationData, response)
    
    return response
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', '/competitions/qualifications', qualificationData, normalizedError)
    throw normalizedError
  }
}

/**
 * Get competition credit history
 * @param {string} competitionId - Competition ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Credit history for the competition
 */
export const getCompetitionCreditHistory = async (competitionId, additionalParams = {}) => {
  try {
    logApiCall('GET', `/competitions/${competitionId}/credits`, null, null)
    
    const creditHistoryResponse = await campAdminApi.getCreditHistory({
      competition_id: competitionId,
      ...additionalParams
    })
    
    logApiCall('GET', `/competitions/${competitionId}/credits`, null, creditHistoryResponse)
    
    return creditHistoryResponse
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `/competitions/${competitionId}/credits`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Get dashboard data for competitions overview
 * @returns {Promise<Object>} Dashboard data response
 */
export const getCompetitionsDashboardData = async () => {
  try {
    logApiCall('GET', '/competitions/dashboard', null, null)
    
    // Combine data from multiple sources
    const [competitionsResponse, summaryStatsResponse, routesResponse] = await Promise.all([
      getCompetitions({ limit: 10, offset:0 }), // Get recent competitions
      analyticsApi.getSummaryStatistics(),
      getRoutes({ limit: 10,offset:0 })
    ])
    
    const dashboardData = {
      recent_competitions: competitionsResponse.data,
      summary_statistics: summaryStatsResponse.data,
      available_routes: routesResponse.data,
      total_competitions: competitionsResponse.total
    }
    
    const result = {
      success: true,
      data: dashboardData
    }
    
    logApiCall('GET', '/competitions/dashboard', null, result)
    
    return result
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', '/competitions/dashboard', null, normalizedError)
    throw normalizedError
  }
}

// Export all functions as a service object
export const competitionsApi = {
  getCompetitions,
  getCompetitionStats,
  searchCompetitions,
  getCompetitionsByRoute,
  getRoutes,
  getCompetitionQualifications,
  createCompetitionQualification,
  getCompetitionCreditHistory,
  getCompetitionsDashboardData
}
