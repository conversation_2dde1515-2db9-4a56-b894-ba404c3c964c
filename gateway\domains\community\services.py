"""
Community business logic and data access services.

Contains all business logic for community including data access,
event management, and member interactions.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from bson import ObjectId

from core.config import logger
from core.database.mongo_db import mongo_manager
from core.cache.decorators import cached
from gateway.domains.community.schemas import (
    UniversityListResponse,
    MajorListResponse,
)
from libs.schemas.api.base import PaginationConfig
from libs.schemas.api.common import (
    paginated_response,
    SearchParams,
    SortParams,
    CommunityFilterParams
)
from libs.exceptions import ServiceException, MongoDBError
from libs.errors import CommunityDomainErrors


class CommunityServices:
    """Business logic services for community operations."""

    def __init__(self):
        """Initialize community services."""
        self.mongo_db = mongo_manager

    @cached(
        domain="community",
        operation="universities_list",
        ttl=43200,  # 12 hours
        key_params=["country", "province", "limit", "offset", "search_query", "sort_by"]
    )
    async def get_universities_list(
        self, 
        database=None,
        limit: int = PaginationConfig.DEFAULT_LIMIT,
        offset: int = PaginationConfig.DEFAULT_OFFSET,
        filter_params: Optional[CommunityFilterParams] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None
    ) -> Dict[str, Any]:
        """Retrieve a list of all universities from the database with search, filtering, and sorting."""
        logger.info("Fetching list of universities from database.")
        
        if database is None:
            raise ServiceException(
                code=400,
                errmsg=CommunityDomainErrors.database_error("Database Dependency Error"),
                data={"database": database, "collection": "universities"}
            )

        collection = database["universities"]
        
        # Build MongoDB query from filter parameters
        query = {}
        if filter_params:
            if filter_params.country:
                query["Country"] = filter_params.country
            if filter_params.province:
                query["Province"] = filter_params.province
        
        # Add search conditions
        if search_params and search_params.query:
            search_conditions = []
            for field in search_params.search_fields:
                if search_params.case_sensitive:
                    search_conditions.append({field: {"$regex": search_params.query}})
                else:
                    search_conditions.append({field: {"$regex": search_params.query, "$options": "i"}})
            
            if search_conditions:
                if query:  # If there are existing filters, combine with AND
                    query = {"$and": [query, {"$or": search_conditions}]}
                else:
                    query = {"$or": search_conditions}
        
        try:
            # Get total count first for pagination metadata
            total_count = await collection.count_documents(query)
            
            # Build aggregation pipeline with pagination
            pipeline = [
                {"$match": query},
                {"$skip": offset if offset != PaginationConfig.NO_LIMIT else 0},
                {"$project": {
                    "_id": 0,
                    "university_id": {"$toString": "$_id"},
                    "University": 1, 
                    "Country": 1, 
                    "Province": 1
                }},
            ]
            
            # Add sorting
            if sort_params and sort_params.sort_by:
                sort_direction = 1 if sort_params.sort_order.value == "asc" else -1
                pipeline.insert(-1, {"$sort": {sort_params.sort_by: sort_direction}})
            else:
                # Default sort by University name
                pipeline.insert(-1, {"$sort": {"University": 1}})
            
            # Add limit if specified
            if limit != PaginationConfig.NO_LIMIT:
                pipeline.insert(-1, {"$limit": limit})
            
            response = await collection.aggregate(pipeline).to_list(length=None)

            result = paginated_response(
                data=response,
                total=total_count,
                limit=limit if limit != PaginationConfig.NO_LIMIT else len(response),
                offset=offset,
                message="Universities retrieved successfully"
            )
            
            # Return dict for caching compatibility
            return result.model_dump()
            
        except Exception as e:
            logger.error(f"Database error fetching universities: {e}", exc_info=True)
            raise MongoDBError(
                operation="get_universities_list",
                message=CommunityDomainErrors.database_error(e),
                details={"database": database, "collection": "universities"},
                collection="universities",
            )
            
    @cached(
        domain="community",
        operation="majors_list",
        ttl=43200,  # 12 hours
        key_params=["major", "discipline", "limit", "offset", "search_query", "sort_by"]
    )
    async def get_majors_list(
        self,
        database=None,    
        limit: int = PaginationConfig.DEFAULT_LIMIT,
        offset: int = PaginationConfig.DEFAULT_OFFSET,
        filter_params: Optional[CommunityFilterParams] = None,
        search_params: Optional[SearchParams] = None,
        sort_params: Optional[SortParams] = None
    ) -> Dict[str, Any]:
        """Retrieve a list of all majors from the database with search, filtering, and sorting."""
        logger.info("Fetching list of majors from database.")
        
        if database is None:
            raise ServiceException(
                code=400,
                errmsg=CommunityDomainErrors.database_error("Database Dependency Error"),
                data={"database": database, "collection": "majors"}
            )
            
        collection = database["majors"]
        
        # Build MongoDB query from filter parameters
        query = {}
        if filter_params:
            if filter_params.major:
                query["Major"] = filter_params.major
            if filter_params.discipline:
                query["Discipline"] = filter_params.discipline
        
        # Add search conditions
        if search_params and search_params.query:
            search_conditions = []
            for field in search_params.search_fields:
                if search_params.case_sensitive:
                    search_conditions.append({field: {"$regex": search_params.query}})
                else:
                    search_conditions.append({field: {"$regex": search_params.query, "$options": "i"}})
            
            if search_conditions:
                if query:  # If there are existing filters, combine with AND
                    query = {"$and": [query, {"$or": search_conditions}]}
                else:
                    query = {"$or": search_conditions}
    
        try:
            # Get total count first for pagination metadata
            total_count = await collection.count_documents(query)
            
            # Build aggregation pipeline with pagination
            pipeline = [
                {"$match": query},
                {"$skip": offset if offset != PaginationConfig.NO_LIMIT else 0},
                {"$project": {
                    "_id": 0,
                    "major_id": {"$toString": "$_id"},
                    "Major": 1, 
                    "Discipline": 1
                }},
            ]
            
            # Add sorting
            if sort_params and sort_params.sort_by:
                sort_direction = 1 if sort_params.sort_order.value == "asc" else -1
                pipeline.insert(-1, {"$sort": {sort_params.sort_by: sort_direction}})
            else:
                # Default sort by Major name
                pipeline.insert(-1, {"$sort": {"Major": 1}})
            
            # Add limit if specified
            if limit != PaginationConfig.NO_LIMIT:
                pipeline.insert(-1, {"$limit": limit})

            response = await collection.aggregate(pipeline).to_list(length=None)

            result = paginated_response(
                data=response,
                total=total_count,
                limit=limit if limit != PaginationConfig.NO_LIMIT else len(response),
                offset=offset,
                message="Majors retrieved successfully"
            )
            
            # Return dict for caching compatibility
            return result.model_dump()
            
        except Exception as e:
            logger.error(f"Database error fetching majors: {e}", exc_info=True)
            raise MongoDBError(
                operation="get_majors_list",
                message=CommunityDomainErrors.database_error(e),
                details={"database": database, "collection": "majors"},
                collection="majors",
            )

    @cached(
        domain="community", 
        operation="content_creators",
        ttl=1800,  # 30 minutes
        key_params=["start_date"]
    )
    async def get_content_creators(self, 
                                   start_date: str,
                                   database=None) -> List[Dict[str, Any]]:
        """Get content creators from MongoDB."""
        try:
            database = await self.mongo_db.get_database()

            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
            start_object_id = ObjectId.from_datetime(start_datetime)

            pipeline = [
                {"$match": {"_id": {"$gte": start_object_id}}},
                {
                    "$project": {
                        "_id": 0,
                        "user_id": {"$toString": "$_id"},
                        "name": "$Name",
                        "email": "$Email",
                        "phone": "$Phone",
                        "university": "$University.University",
                        "identity": "$Identity",
                        "experience": "$Experience",
                        "join_date": {
                            "$dateToString": {
                                "format": "%Y-%m-%d %H:%M:%S",
                                "timezone": "Asia/Shanghai",
                                "date": "$JoinDate",
                            }
                        },
                    }
                },
            ]

            cursor = database["users"].aggregate(pipeline)
            creators = await cursor.to_list(length=None)

            logger.info(f"Retrieved {len(creators)} content creators")
            return creators

        except Exception as e:
            logger.error(f"Failed to get content creators: {e}")
            raise


# Global services instance
community_service = CommunityServices()
