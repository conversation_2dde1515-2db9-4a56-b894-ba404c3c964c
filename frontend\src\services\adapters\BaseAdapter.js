/**
 * Base adapter class with common transformation methods
 */
export class BaseAdapter {
  /**
   * Transform a single item
   * @param {Object} item - Item to transform
   * @param {string} type - Type of transformation
   * @returns {Object} Transformed item
   */
  static transformItem(item, type = 'default') {
    if (!item) return null
    
    // Apply common transformations
    const transformed = {
      ...item,
      // Ensure consistent ID field
      id: item.id || item.university_id || item.competition_id || item.user_id,
      // Add timestamps if missing
      createdAt: item.created_at || item.createdAt || new Date().toISOString(),
      updatedAt: item.updated_at || item.updatedAt || new Date().toISOString()
    }
    
    return transformed
  }
  
  /**
   * Transform a list of items
   * @param {Array} items - Items to transform
   * @param {string} type - Type of transformation
   * @returns {Array} Transformed items
   */
  static transformList(items, type = 'default') {
    if (!Array.isArray(items)) return []
    
    return items.map(item => this.transformItem(item, type))
  }
  
  /**
   * Transform paginated response
   * @param {Object} response - API response with pagination
   * @param {string} type - Type of transformation
   * @returns {Object} Transformed paginated response
   */
  static transformPaginated(response, type = 'default') {
    if (!response || !response.data) {
      return {
        data: [],
        pagination: {
          total: 0,
          limit: 20,
          offset: 0,
          page: 1,
          total_pages: 0,
          has_next: false,
          has_prev: false
        }
      }
    }

    const data = Array.isArray(response.data) ? response.data : response.data.res || []

    // Handle new standardized pagination structure
    const pagination = response.pagination || {}
    const total = pagination.total || response.total || data.length
    const limit = pagination.limit || response.limit || response.page_size || 20
    const offset = pagination.offset || response.offset || 0
    const page = pagination.page || (offset ? Math.floor(offset / limit) + 1 : response.current_page || 1)
    const total_pages = pagination.total_pages || Math.ceil(total / limit)
    const has_next = pagination.has_next !== undefined ? pagination.has_next : (page * limit < total)
    const has_prev = pagination.has_prev !== undefined ? pagination.has_prev : (page > 1)

    return {
      data: this.transformList(data, type),
      pagination: {
        total,
        limit,
        offset,
        page,
        total_pages,
        has_next,
        has_prev
      }
    }
  }
  
  /**
   * Normalize error response
   * @param {Object} error - Error object
   * @returns {Object} Normalized error
   */
  static normalizeError(error) {
    return {
      success: false,
      error: {
        code: error.error?.code || error.code || 'UNKNOWN_ERROR',
        message: error.error?.message || error.message || 'An error occurred',
        details: error.error?.details || error.details || null
      },
      status: error.status || 500
    }
  }
  
  /**
   * Ensure success response format
   * @param {Object} data - Response data
   * @param {string} message - Success message
   * @returns {Object} Standardized success response
   */
  static createSuccessResponse(data, message = 'Success') {
    return {
      success: true,
      data,
      message
    }
  }
}

/**
 * Generic adapter for simple transformations
 */
export class GenericAdapter extends BaseAdapter {
  /**
   * Transform any response to standard format
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transform(response, options = {}) {
    const { type = 'default', isPaginated = false } = options
    
    if (isPaginated) {
      return this.transformPaginated(response, type)
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: this.transformList(response.data, type)
      }
    }
    
    return {
      ...response,
      data: this.transformItem(response.data, type)
    }
  }
}

/**
 * Adapter registry for dynamic adapter selection
 */
export class AdapterRegistry {
  static adapters = new Map()
  
  /**
   * Register an adapter
   * @param {string} type - Adapter type
   * @param {Object} adapter - Adapter class
   */
  static register(type, adapter) {
    this.adapters.set(type, adapter)
  }
  
  /**
   * Get adapter by type
   * @param {string} type - Adapter type
   * @returns {Object} Adapter class
   */
  static get(type) {
    return this.adapters.get(type) || GenericAdapter
  }
  
  /**
   * Transform response using registered adapter
   * @param {string} type - Adapter type
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transform(type, response, options = {}) {
    const adapter = this.get(type)
    return adapter.transform(response, options)
  }
}
