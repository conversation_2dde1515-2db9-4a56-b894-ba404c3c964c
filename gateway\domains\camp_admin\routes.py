"""
Competition Management API Router.

Handles all competition-related endpoints organized by business function:
- Routes (high-level categories)
- Competitions (events within routes)
- Qualifications (standards for earning credits)
- Credits (points earned through qualifications)
- Badges (rewards for accumulated credits)
"""


from fastapi import APIRouter, Query, Body, Depends
from typing import Optional, Dict, Any, List
import logging

from .schemas import (
    QualificationCreateRequest,
    CreditAwardRequest,
    CreditRevokeRequest,
    AdminLogsCreateRequest,
    RouteListResponse,
    CompetitionListResponse,
    QualificationListResponse,
    CreditLogListResponse,
    AdminLogsListResponse,
)
from .auth_routes import router as auth_router
from libs.auth.authentication import (
    get_current_user_id,
    get_current_admin_id,
)
from libs.validation import validate_pagination
from core.database.dependencies import (
    get_read_only_session,
    get_transaction_session,
)
from libs.schemas.api.responses import (
    success,
    SuccessResponse,
    ErrorResponse,
)
from libs.schemas.api.base import PaginationConfig,BaseDataPostResponse
from libs.schemas.api.common import (
    SearchParams,
    SortParams,
    SortOrder,
    CampAdminFilterParams
)
from .services import camp_admin_services

logger = logging.getLogger(__name__)

# Create router with prefix for competition endpoints
router = APIRouter(
    prefix="/camp-admin",
    tags=["camp-admin"],
    responses={404: {"description": "Not found"}},
)

# Include auth routes
router.include_router(auth_router)

# Routes Management
@router.get(
    "/routes",
    response_model=RouteListResponse | ErrorResponse,
    summary="获取活动赛道",
    description="获取所有活动赛道，支持搜索和排序",
)
async def list_routes(
    # Pagination parameters
    limit: int = Query(PaginationConfig.NO_LIMIT, le=100, description="Number of routes to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of routes to skip"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for route names and info"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (route_name, route_info)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (route_name, created_at)"),
    sort_order: SortOrder = Query(SortOrder.ASC, description="Sort order (asc/desc)"),
    
    user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),

) -> Dict[str, Any]:
    """List all available competition routes with search and sorting."""
    
    # Build search parameters
    search_params = None
    if search_query:
        search_params = SearchParams(
            query=search_query,
            search_fields=search_fields,
            case_sensitive=case_sensitive
        )
    
    # Build sort parameters
    sort_params = None
    if sort_by:
        sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
    
    result = await camp_admin_services.get_routes(
        limit=limit, 
        offset=offset,
        search_params=search_params,
        sort_params=sort_params,
        db_session=db_session
    )
    return result


# Competition Management
@router.get(
    "/",
    response_model=CompetitionListResponse | ErrorResponse,
    summary="获取活动",
    description="获取所有活动，支持按赛道、状态筛选，支持搜索和排序",
)
async def list_competitions(
    # Pagination parameters
    limit: int = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of competitions to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of competitions to skip"),
    
    # Filter parameters
    route_id: Optional[str] = Query(None, description="Filter by route ID"),
    status: Optional[str] = Query(None, description="Filter by competition status"),
    start_date: Optional[str] = Query(None, description="Filter by start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="Filter by end date (ISO format)"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for competition names, route names, and IDs"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (competition_name, route_name, competition_id)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (competition_name, route_name, created_at)"),
    sort_order: SortOrder = Query(SortOrder.ASC, description="Sort order (asc/desc)"),
    
    user_id: str = Depends(get_current_user_id),    
    db_session = Depends(get_read_only_session), 
) -> Dict[str, Any]:
    """List competitions with enhanced filtering, search, and sorting."""
    
    # Build filter parameters
    filter_params = CampAdminFilterParams(
        route_id=route_id,
        status=status,
        start_date=start_date,
        end_date=end_date
    )
    
    # Build search parameters
    search_params = None
    if search_query:
        search_params = SearchParams(
            query=search_query,
            search_fields=search_fields,
            case_sensitive=case_sensitive
        )
    
    # Build sort parameters
    sort_params = None
    if sort_by:
        sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
    
    result = await camp_admin_services.get_competitions(
        filter_params=filter_params,
        search_params=search_params,
        sort_params=sort_params,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return result


# Qualification Management
@router.get(
    "/qualifications",
    response_model=QualificationListResponse | ErrorResponse,
    summary="获取活动得分规则",
    description="获取活动得分规则，支持按活动筛选，支持搜索和排序",
)
async def list_qualifications(
    # Filter parameters
    competition_id: Optional[str] = Query(None, description="Filter by competition ID"),
    qualification_id: Optional[str] = Query(None, description="Filter by qualification ID"),
    
    # Pagination parameters
    limit: int = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of qualifications to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of qualifications to skip"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for qualification names, competition names, and IDs"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (qualification_name, competition_name, competition_id)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (qualification_name, competition_name, credit, created_at)"),
    sort_order: SortOrder = Query(SortOrder.ASC, description="Sort order (asc/desc)"),
    
    user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
) -> Dict[str, Any]:
    """List qualifications with enhanced filtering, search, and sorting."""
    
    # Build filter parameters
    filter_params = CampAdminFilterParams(
        competition_id=competition_id,
        qualification_id=qualification_id
    )
    
    # Build search parameters
    search_params = None
    if search_query:
        search_params = SearchParams(
            query=search_query,
            search_fields=search_fields,
            case_sensitive=case_sensitive
        )
    
    # Build sort parameters
    sort_params = None
    if sort_by:
        sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
    
    result = await camp_admin_services.get_qualifications(
        filter_params=filter_params,
        search_params=search_params,
        sort_params=sort_params,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return result


@router.post(
    "/qualifications",
    response_model=SuccessResponse | ErrorResponse,
    summary="创建活动得分规则",
    description="创建活动得分规则，仅限管理员操作",
)
async def create_qualification(
    qualification_data: QualificationCreateRequest = Body(...,
                                                          examples=[
                                                              {
                                                                  "competition_id": "123",
                                                                  "credit": 100,
                                                                  "qualification_type": 1,
                                                                  "qualification_logic": 1,
                                                                  "qualification_name": "得分规则名称",
                                                                  "related_task_id": "123"
                                                              }
                                                          ]),
    user_id: str = Depends(get_current_user_id),
    tx_session = Depends(get_transaction_session),
) -> Dict[str, Any]:
    """Create a new qualification standard."""

    result = await camp_admin_services.create_qualification(
        qualification_data=qualification_data,
        db_session=tx_session,
    )
    return result


# Credit Management
@router.get(
    "/credits/history",
    response_model=CreditLogListResponse | ErrorResponse,
    summary="获取学分发放历史记录",
    description="获取学分发放历史记录，支持按用户、活动筛选，支持搜索和排序",
)
async def get_credit_history(
    current_user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
    
    # Filter parameters
    competition_id: Optional[str] = Query(None, description="Filter by competition ID"),
    user_id_filter: Optional[str] = Query(
        None, alias="user_id", description="Filter by user ID"
    ),
    start_date: Optional[str] = Query(None, description="Filter by start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="Filter by end date (ISO format)"),
    
    # Pagination parameters
    limit: int = Query(200, ge=1, le=1000, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for user names, competition names, qualification names, remarks, and universities"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (user_name, competition_name, qualification_name, remark, university)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (created_at, credit, user_name, competition_name)"),
    sort_order: SortOrder = Query(SortOrder.DESC, description="Sort order (asc/desc)"),
) -> Dict[str, Any]:
    """Get credit transaction history with enhanced filtering, search, and sorting."""
    
    # Build filter parameters
    filter_params = CampAdminFilterParams(
        competition_id=competition_id,
        user_id=user_id_filter,
        start_date=start_date,
        end_date=end_date
    )
    
    # Build search parameters
    search_params = None
    if search_query:
        search_params = SearchParams(
            query=search_query,
            search_fields=search_fields,
            case_sensitive=case_sensitive
        )
    
    # Build sort parameters
    sort_params = None
    if sort_by:
        sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
    
    result = await camp_admin_services.get_credit_logs(
        filter_params=filter_params,
        search_params=search_params,
        sort_params=sort_params,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return result


@router.post(
    "/credits/award",
    response_model=SuccessResponse | ErrorResponse,
    summary="学分发放",
    description="学分发放，可以按照单一用户 id 或活动 id 进行筛选",
)
async def award_credits(
    credit_data: CreditAwardRequest = Body(...),
    admin_id: str = Depends(get_current_admin_id),
    tx_session = Depends(get_transaction_session),
) -> Dict[str, Any]:
    """Award credits to a user for qualifying in a competition."""
    result = await camp_admin_services.award_credits(
        credit_log_data=credit_data,
        db_session=tx_session,
    )
    return result


@router.post(
    "/credits/revoke",
    response_model=SuccessResponse | ErrorResponse,
    summary="撤销学分发放",
    description="撤销学分发放，仅限管理员操作，必须提供撤销原因",
)
async def revoke_credits(
    revoke_data: CreditRevokeRequest = Body(...),
    admin_id: str = Depends(get_current_admin_id),
    tx_session = Depends(get_transaction_session),
) -> Dict[str, Any]:
    """Revoke a previously awarded credit record."""
    result = await camp_admin_services.revoke_credits(
        record_id=revoke_data.record_id,
        reason="Admin revocation",
        tx_session=tx_session,
    )
    return result

# admin logs management
@router.get(
    "/logs",
    response_model=AdminLogsListResponse | ErrorResponse,
    summary="获取管理员操作日志",
    description="获取管理员操作日志，支持按操作类型和用户筛选，支持搜索和排序",
)
async def get_admin_logs(
    # Pagination parameters
    limit: int = Query(20, ge=1, le=100, description="Number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    
    # Filter parameters
    operation: Optional[str] = Query(None, description="Filter by operation type"),
    user_id_filter: Optional[str] = Query(None, alias="user_id", description="Filter by user ID"),
    start_date: Optional[str] = Query(None, description="Filter by start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="Filter by end date (ISO format)"),
    
    # Search parameters
    search_query: Optional[str] = Query(None, alias="q", description="Search query for operation types, remarks, and user IDs"),
    search_fields: Optional[List[str]] = Query(None, description="Fields to search in (operation, remark, user_id)"),
    case_sensitive: bool = Query(False, description="Whether search is case sensitive"),
    
    # Sort parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by (created_at, operation, user_id)"),
    sort_order: SortOrder = Query(SortOrder.DESC, description="Sort order (asc/desc)"),
    
    user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
) -> Dict[str, Any]: 
    """Get admin operation logs with enhanced filtering, search, and sorting."""
    
    # Build filter parameters
    filter_params = CampAdminFilterParams(
        operation=operation,
        user_id=user_id_filter,
        start_date=start_date,
        end_date=end_date
    )
    
    # Build search parameters
    search_params = None
    if search_query:
        search_params = SearchParams(
            query=search_query,
            search_fields=search_fields,
            case_sensitive=case_sensitive
        )
    
    # Build sort parameters
    sort_params = None
    if sort_by:
        sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
    
    result = await camp_admin_services.get_admin_logs(
        filter_params=filter_params,
        search_params=search_params,
        sort_params=sort_params,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return result


@router.post(
    "/logs/create", 
    response_model=SuccessResponse | ErrorResponse,
    summary="【慎用】创建管理员操作日志",
    description="创建管理员操作日志，仅限超级管理员操作",
    
)
async def create_admin_log(
    log_data: AdminLogsCreateRequest = Body(...),
    user_id: str = Depends(get_current_user_id),
    tx_session = Depends(get_transaction_session),
) -> Dict[str, Any]:
    """Create a new admin log."""
    log_data.admin_email_id = user_id
    result = await camp_admin_services.create_admin_log(log_data=log_data, db_session=tx_session)
    return result
