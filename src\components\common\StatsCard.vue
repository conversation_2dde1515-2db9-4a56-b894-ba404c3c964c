<template>
  <el-card class="stats-card" :class="cardClass" shadow="hover">
    <div class="stats-content">
      <!-- Icon -->
      <div class="stats-icon" v-if="icon || $slots.icon">
        <slot name="icon">
          <el-icon :size="iconSize" :color="iconColor">
            <component :is="icon" />
          </el-icon>
        </slot>
      </div>
      
      <!-- Main content -->
      <div class="stats-main">
        <!-- Title -->
        <div class="stats-title">
          {{ title }}
        </div>
        
        <!-- Value -->
        <div class="stats-value">
          <span class="value-number">{{ formattedValue }}</span>
          <span v-if="unit" class="value-unit">{{ unit }}</span>
        </div>
        
        <!-- Change indicator -->
        <div v-if="change !== undefined" class="stats-change" :class="changeClass">
          <el-icon :size="14">
            <ArrowUp v-if="changeType === 'increase'" />
            <ArrowDown v-if="changeType === 'decrease'" />
            <Minus v-if="changeType === 'neutral'" />
          </el-icon>
          <span>{{ formattedChange }}</span>
          <span v-if="changePeriod" class="change-period">{{ changePeriod }}</span>
        </div>
        
        <!-- Description -->
        <div v-if="description" class="stats-description">
          {{ description }}
        </div>
      </div>
      
      <!-- Action button -->
      <div v-if="actionText || $slots.action" class="stats-action">
        <slot name="action">
          <el-button
            :type="actionType"
            :size="actionSize"
            @click="handleAction"
            text
          >
            {{ actionText }}
          </el-button>
        </slot>
      </div>
    </div>
    
    <!-- Footer slot -->
    <div v-if="$slots.footer" class="stats-footer">
      <slot name="footer" />
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // Main content
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  displayValue: {
    type: String,
    default: null
  },
  unit: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  
  // Icon
  icon: {
    type: [String, Object],
    default: null
  },
  iconSize: {
    type: [Number, String],
    default: 24
  },
  iconColor: {
    type: String,
    default: ''
  },
  
  // Change indicator
  change: {
    type: Number,
    default: undefined
  },
  changeType: {
    type: String,
    default: 'neutral',
    validator: (value) => ['increase', 'decrease', 'neutral'].includes(value)
  },
  changePeriod: {
    type: String,
    default: ''
  },
  
  // Formatting
  format: {
    type: String,
    default: 'number',
    validator: (value) => ['number', 'percentage', 'currency', 'decimal'].includes(value)
  },
  precision: {
    type: Number,
    default: 0
  },
  
  // Styling
  color: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  
  // Action
  actionText: {
    type: String,
    default: ''
  },
  actionType: {
    type: String,
    default: 'primary'
  },
  actionSize: {
    type: String,
    default: 'small'
  },
  
  // Loading state
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['action'])

// Computed
const cardClass = computed(() => {
  return [
    `stats-card--${props.color}`,
    `stats-card--${props.size}`,
    {
      'stats-card--loading': props.loading
    }
  ]
})

const formattedValue = computed(() => {
  if (props.loading) return '--'

  // Use backend-provided display value if available
  if (props.displayValue) {
    return props.displayValue
  }

  const value = Number(props.value)

  switch (props.format) {
    case 'percentage':
      return `${value.toFixed(props.precision)}%`
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: props.precision,
        maximumFractionDigits: props.precision
      }).format(value)
    case 'decimal':
      return value.toFixed(props.precision)
    case 'number':
    default:
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`
      }
      return value.toLocaleString()
  }
})

const formattedChange = computed(() => {
  if (props.change === undefined) return ''
  
  const absChange = Math.abs(props.change)
  const sign = props.change >= 0 ? '+' : '-'
  
  if (props.format === 'percentage') {
    return `${sign}${absChange.toFixed(1)}%`
  }
  
  return `${sign}${absChange.toFixed(1)}`
})

const changeClass = computed(() => {
  return [
    `stats-change--${props.changeType}`,
    {
      'stats-change--positive': props.change > 0,
      'stats-change--negative': props.change < 0,
      'stats-change--neutral': props.change === 0
    }
  ]
})

// Methods
const handleAction = () => {
  emit('action')
}
</script>

<style lang="scss" scoped>
.stats-card {
  height: 100%;
  
  :deep(.el-card__body) {
    padding: 20px;
    height: 100%;
  }
  
  .stats-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    height: 100%;
  }
  
  .stats-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
  
  .stats-main {
    flex: 1;
    min-width: 0;
  }
  
  .stats-title {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-bottom: 8px;
    line-height: 1.4;
  }
  
  .stats-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 8px;
    
    .value-number {
      font-size: 28px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      line-height: 1;
    }
    
    .value-unit {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
  
  .stats-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    margin-bottom: 4px;
    
    &--increase {
      color: var(--el-color-success);
    }
    
    &--decrease {
      color: var(--el-color-danger);
    }
    
    &--neutral {
      color: var(--el-text-color-regular);
    }
    
    .change-period {
      color: var(--el-text-color-placeholder);
      margin-left: 4px;
    }
  }
  
  .stats-description {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    line-height: 1.4;
  }
  
  .stats-action {
    flex-shrink: 0;
  }
  
  .stats-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
  
  // Size variations
  &--small {
    .stats-icon {
      width: 40px;
      height: 40px;
    }
    
    .stats-value .value-number {
      font-size: 24px;
    }
  }
  
  &--large {
    .stats-icon {
      width: 56px;
      height: 56px;
    }
    
    .stats-value .value-number {
      font-size: 32px;
    }
  }
  
  // Color variations
  &--success .stats-icon {
    background-color: var(--el-color-success-light-9);
    color: var(--el-color-success);
  }
  
  &--warning .stats-icon {
    background-color: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
  }
  
  &--danger .stats-icon {
    background-color: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
  }
  
  &--info .stats-icon {
    background-color: var(--el-color-info-light-9);
    color: var(--el-color-info);
  }
  
  // Loading state
  &--loading {
    .stats-value .value-number {
      color: var(--el-text-color-placeholder);
    }
  }
}

@media (max-width: 768px) {
  .stats-card {
    .stats-content {
      flex-direction: column;
      gap: 12px;
    }
    
    .stats-icon {
      align-self: flex-start;
    }
  }
}
</style>
