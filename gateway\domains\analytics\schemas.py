"""
Analytics Domain Schemas.

Request and response models for analytics endpoints including:
- Rankings (school and user)
- Statistics
- Event tracking
- ShenCe analytics integration
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, model_validator, computed_field
from datetime import datetime

# ——— Import shared base schemas ———
from libs.schemas.api.common import PaginatedResponse, StandardResponse


# ——— Migrated from shared/schemas/api/rank.py with Standardization ———


class TopNByRoute(BaseModel):
    top_num: int = Field(gte=1, description="topN数量")


class TopNByRouteData(BaseModel):
    route_name: str = Field(description="赛道名称")
    user_ids: list[str] = Field(description="用户id列表")
    user_names: list[str] = Field(description="用户名称列表")
    total_credits: list[float] = Field(description="用户积分列表", ge=0.0)

    @model_validator(mode="after")
    def check_same_length(self) -> "TopNByRouteData":
        user_ids = self.user_ids
        user_names = self.user_names
        total_credits = self.total_credits

        if user_ids is None or user_names is None or total_credits is None:
            raise ValueError(
                "user_ids, user_names, and total_credits must all be provided"
            )

        if not (len(user_ids) == len(user_names) == len(total_credits)):
            raise ValueError(
                "user_ids, user_names, and total_credits must have the same length"
            )
        return self
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for route ranking."""
        user_count = len(self.user_names) if self.user_names else 0
        return f"{self.route_name} 排行榜 (前{user_count}名)"
    
    @computed_field
    @property
    def display_summary(self) -> str:
        """Summary of top performers."""
        if not self.user_names or not self.total_credits:
            return f"{self.route_name}: 暂无数据"
        
        top_user = self.user_names[0]
        top_credits = self.total_credits[0]
        return f"{self.route_name}: {top_user} 领先 ({top_credits}学分)"


class TopNByRouteResponse(StandardResponse[List[TopNByRouteData]]):
    """Standardized response for top N users by route."""
    pass


class SchoolRanking(BaseModel):
    page_size: int = Field(ge=1, description="返回数量")
    current_page: int = Field(default=1, ge=1, description="当前页数, 从1开始")


class SchoolRankingElement(BaseModel):
    university: str = Field(description="学校名称")
    total_credits: float = Field(description="总积分", ge=0.0)
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for school."""
        return self.university
    
    @computed_field
    @property
    def display_credits(self) -> str:
        """Formatted credit amount."""
        return f"{self.total_credits:.1f} 学分"
    
    @computed_field
    @property
    def display_status(self) -> str:
        """Performance status indicator."""
        if self.total_credits >= 1000:
            return "优秀"
        elif self.total_credits >= 500:
            return "良好" 
        elif self.total_credits >= 100:
            return "一般"
        else:
            return "待提升"


class SchoolRankingData(BaseModel):
    total: int = Field(description="数据总数")
    page_size: int = Field(description="返回数量")
    current_page: int = Field(description="当前页数, 从1开始")
    res: List[SchoolRankingElement]


class SchoolRankingResponse(StandardResponse[SchoolRankingData]):
    """Standardized response for school rankings."""
    pass


class UserRankingEveryRoute(BaseModel):
    top_num: int = Field(default=30, gte=1, description="topN数量")


class UserRankingEveryRouteElement(BaseModel):
    user_id: str = Field(description="用户id")
    user_name: str = Field(description="用户名称")
    total_credits: float = Field(description="用户积分", ge=0.0)
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for user."""
        return self.user_name or f"用户 {self.user_id[-4:]}"
    
    @computed_field
    @property
    def display_credits(self) -> str:
        """Formatted credit amount."""
        return f"{self.total_credits:.1f} 学分"
    
    @computed_field
    @property
    def display_user_id(self) -> str:
        """Masked user ID for privacy."""
        return f"****{self.user_id[-4:]}"


class UserRankingEveryRouteEachData(BaseModel):
    route_name: str = Field(description="赛道名称")
    ranking: List[UserRankingEveryRouteElement]
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for route ranking."""
        user_count = len(self.ranking) if self.ranking else 0
        return f"{self.route_name} 用户排行 (共{user_count}人)"


class UserRankingEveryRouteResponse(StandardResponse[List[UserRankingEveryRouteEachData]]):
    """Standardized response for user ranking by every route."""
    pass


class UserRankingTotalRoute(BaseModel):
    page_size: int = Field(ge=1, description="返回数量")
    current_page: int = Field(default=1, ge=1, description="当前页数, 从1开始")


class UserRankingTotalRouteElement(BaseModel):
    route_name: str = Field(description="赛道名称")
    credit: float = Field(description="积分", ge=0.0)
    
    @computed_field
    @property
    def display_credits(self) -> str:
        """Formatted credit amount."""
        return f"{self.credit:.1f} 学分"


class UserRankingTotalRouteEachData(BaseModel):
    user_id: str = Field(description="用户id")
    user_name: str = Field(description="用户名称")
    credits_each: List[UserRankingTotalRouteElement] = Field(description="各赛道的积分")
    total_credits: float = Field(description="总积分", ge=0.0)
    max_credits: float = Field(description="最高积分", ge=0.0)
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for user."""
        return self.user_name or f"用户 {self.user_id[-4:]}"
    
    @computed_field
    @property
    def display_total_credits(self) -> str:
        """Formatted total credit amount."""
        return f"{self.total_credits:.1f} 学分"
    
    @computed_field
    @property
    def display_status(self) -> str:
        """User performance status."""
        if self.total_credits >= 500:
            return "顶尖选手"
        elif self.total_credits >= 200:
            return "优秀选手"
        elif self.total_credits >= 50:
            return "活跃选手"
        else:
            return "新手选手"
    
    @computed_field
    @property
    def display_user_id(self) -> str:
        """Masked user ID for privacy."""
        return f"****{self.user_id[-4:]}"


class UserRankingTotalRouteData(BaseModel):
    total: int = Field(description="数据总数")
    page_size: int = Field(description="返回数量")
    current_page: int = Field(description="当前页数, 从1开始")
    res: List[UserRankingTotalRouteEachData]


class UserRankingTotalRouteResponse(StandardResponse[UserRankingTotalRouteData]):
    """Standardized response for user total route rankings."""
    pass


class SummaryStatistics(BaseModel):
    statistics_name: str = Field(description="统计指标名称")
    statistics_value: int = Field(default=0, description="统计指标值")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly display name for statistic."""
        name_mapping = {
            "total_users": "用户总数",
            "total_competitions": "比赛总数", 
            "total_schools": "学校总数",
            "total_credits": "学分总数",
            "active_users": "活跃用户",
            "completed_competitions": "已结束比赛"
        }
        return name_mapping.get(self.statistics_name, self.statistics_name)
    
    @computed_field
    @property
    def display_value(self) -> str:
        """Formatted statistic value."""
        if self.statistics_value >= 10000:
            return f"{self.statistics_value/10000:.1f}万"
        elif self.statistics_value >= 1000:
            return f"{self.statistics_value/1000:.1f}千"
        else:
            return str(self.statistics_value)


class SummaryStatisticsResponse(StandardResponse[List[SummaryStatistics]]):
    """Standardized response for summary statistics."""
    pass


# ——— Original domain schemas with Standardization ———


class AnalyticsHealthResponse(BaseModel):
    """Health check response for analytics services."""

    status: str = Field(description="Service status")
    rankings_available: bool = Field(description="Rankings service availability")
    statistics_available: bool = Field(description="Statistics service availability")
    event_tracking_available: bool = Field(
        description="Event tracking service availability"
    )


class RankingFilters(BaseModel):
    """Common filters for ranking queries."""

    route_id: str = Field(None, description="Filter by specific route")
    university: str = Field(None, description="Filter by university")
    date_from: str = Field(None, description="Start date for filtering (YYYY-MM-DD)")
    date_to: str = Field(None, description="End date for filtering (YYYY-MM-DD)")


class EventTrackingRequest(BaseModel):
    """Request model for event tracking operations."""

    event_type: str = Field(description="Type of event to track")
    user_id: str = Field(None, description="User ID associated with the event")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional event metadata"
    )


# ——— ShenCe Integration Schemas ———


class ShenceTagCreateRequest(BaseModel):
    """Request schema for creating ShenCe analytics tags."""

    tag_name: str = Field(..., description="Name of the tag to create")
    tag_id: Optional[str] = Field(None, description="Existing tag ID to update")
    dir_id: str = Field(..., description="Directory ID for the tag")
    data: List[Dict[str, Any]] = Field(..., description="User data to upload")
    data_type: Optional[str] = Field("STRING", description="Data type for the tag")


class ShenceFileUploadRequest(BaseModel):
    """Request schema for ShenCe file uploads."""

    file_name_prefix: str = Field(..., description="Prefix for the uploaded file")
    csv_data: List[Dict[str, Any]] = Field(
        ..., description="CSV data as list of dictionaries"
    )
    project: Optional[str] = Field(None, description="ShenCe project name")


class ShenceTagUpdateRequest(BaseModel):
    """Request schema for updating ShenCe analytics tags."""

    file_name: str = Field(..., description="Name of the uploaded file")
    tag_name: str = Field(..., description="Name of the tag")
    tag_id: Optional[str] = Field(None, description="Existing tag ID")
    dir_id: str = Field(..., description="Directory ID")
    data_type: Optional[str] = Field("STRING", description="Data type")


class ShenceDirectoryResponse(BaseModel):
    """Response schema for ShenCe directory metadata."""
    
    directories: List[Dict[str, Any]]
    total_count: int
    project: str


class ShenceTagMetaResponse(BaseModel):
    """Response schema for ShenCe tag metadata."""

    tag_id: str
    name: str
    cname: str
    data_type: str
    dir_id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ShenceUploadResponse(BaseModel):
    """Response schema for ShenCe file uploads."""

    success: bool
    file_name: str
    file_size: int
    upload_id: Optional[str] = None
    message: Optional[str] = None


class ShenceTagResponse(BaseModel):
    """Response schema for ShenCe tag operations."""

    success: bool
    tag_id: Optional[str] = None
    file_name: Optional[str] = None
    upload_response: Optional[Dict[str, Any]] = None
    update_response: Optional[Dict[str, Any]] = None
    message: Optional[str] = None


# ——— New Phase 2.3 Schemas - Missing Endpoints ———


class DashboardData(BaseModel):
    """Dashboard data aggregating key metrics."""
    summary_statistics: List[SummaryStatistics] = Field(description="Core platform statistics")
    user_activity: Dict[str, Any] = Field(description="User activity metrics")
    top_schools: List[SchoolRankingElement] = Field(description="Top performing schools")
    route_rankings: List[TopNByRouteData] = Field(description="Route-wise rankings")
    generation_timestamp: datetime = Field(description="When data was generated")
    user_context: Optional[Dict[str, Any]] = Field(None, description="User-specific context if applicable")
    
    @computed_field
    @property
    def display_generation_time(self) -> str:
        """Formatted generation timestamp."""
        return self.generation_timestamp.strftime("%Y年%m月%d日 %H:%M")


class DashboardResponse(StandardResponse[DashboardData]):
    """Standardized response for dashboard data."""
    pass


class CompetitionStats(BaseModel):
    """Statistics for a specific competition."""
    competition_id: str = Field(description="Competition identifier")
    competition_name: str = Field(description="Competition name")
    participant_count: int = Field(ge=0, description="Number of participants")
    submission_count: int = Field(ge=0, description="Number of submissions")
    qualified_user_count: int = Field(ge=0, description="Number of qualified users")
    total_credits_awarded: float = Field(ge=0.0, description="Total credits awarded")
    average_score: Optional[float] = Field(None, description="Average submission score")
    top_score: Optional[float] = Field(None, description="Highest submission score")
    route_name: str = Field(description="Associated route name")
    start_date: Optional[datetime] = Field(None, description="Competition start date")
    end_date: Optional[datetime] = Field(None, description="Competition end date")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly competition display."""
        return f"{self.competition_name} ({self.route_name})"
    
    @computed_field
    @property
    def display_participation(self) -> str:
        """Formatted participation summary."""
        return f"{self.participant_count}人参赛, {self.submission_count}次提交"
    
    @computed_field
    @property
    def display_credits(self) -> str:
        """Formatted credit summary."""
        return f"共发放 {self.total_credits_awarded:.1f} 学分给 {self.qualified_user_count} 人"


class CompetitionStatsResponse(StandardResponse[CompetitionStats]):
    """Standardized response for competition statistics."""
    pass


class SchoolStats(BaseModel):
    """Statistics for a specific school."""
    university: str = Field(description="School name")
    student_count: int = Field(ge=0, description="Number of registered students")
    total_credits: float = Field(ge=0.0, description="Total credits earned by school")
    average_credits_per_student: float = Field(ge=0.0, description="Average credits per student")
    competition_participation_count: int = Field(ge=0, description="Total competition participations")
    qualified_students_count: int = Field(ge=0, description="Number of students with qualifications")
    top_routes: List[Dict[str, Any]] = Field(description="Top performing routes for this school")
    recent_activity: List[Dict[str, Any]] = Field(description="Recent student activities")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly school display."""
        return self.university
    
    @computed_field
    @property
    def display_summary(self) -> str:
        """Formatted school performance summary."""
        return f"{self.student_count}名学生, 平均{self.average_credits_per_student:.1f}学分/人"
    
    @computed_field
    @property
    def display_performance(self) -> str:
        """Performance indicator for school."""
        if self.average_credits_per_student >= 100:
            return "优秀院校"
        elif self.average_credits_per_student >= 50:
            return "活跃院校"
        else:
            return "参与院校"


class SchoolStatsResponse(StandardResponse[SchoolStats]):
    """Standardized response for school statistics."""
    pass


class UserStats(BaseModel):
    """Statistics for a specific user."""
    user_id: str = Field(description="User identifier")
    user_name: str = Field(description="User name")
    university: Optional[str] = Field(None, description="User's university")
    total_credits: float = Field(ge=0.0, description="Total credits earned")
    competitions_participated: int = Field(ge=0, description="Number of competitions participated")
    qualifications_earned: int = Field(ge=0, description="Number of qualifications earned")
    submissions_count: int = Field(ge=0, description="Total number of submissions")
    average_score: Optional[float] = Field(None, description="Average submission score")
    best_score: Optional[float] = Field(None, description="Best submission score")
    route_performance: List[Dict[str, Any]] = Field(description="Performance by route")
    recent_activities: List[Dict[str, Any]] = Field(description="Recent user activities")
    rank_position: Optional[int] = Field(None, description="Overall rank position")
    
    @computed_field
    @property
    def display_name(self) -> str:
        """User-friendly user display."""
        return self.user_name or f"用户 {self.user_id[-4:]}"
    
    @computed_field
    @property
    def display_summary(self) -> str:
        """Formatted user performance summary."""
        return f"{self.total_credits:.1f}学分, 参与{self.competitions_participated}项比赛"
    
    @computed_field
    @property
    def display_performance_level(self) -> str:
        """Performance level indicator."""
        if self.total_credits >= 500:
            return "顶尖选手"
        elif self.total_credits >= 200:
            return "优秀选手"
        elif self.total_credits >= 50:
            return "活跃选手"
        else:
            return "新手选手"


class UserStatsResponse(StandardResponse[UserStats]):
    """Standardized response for user statistics."""
    pass


class CountResponse(BaseModel):
    """Simple count response for count-only endpoints."""
    count: int = Field(ge=0, description="Total count")
    category: str = Field(description="What is being counted")
    
    @computed_field
    @property
    def display_count(self) -> str:
        """Formatted count display."""
        if self.count >= 10000:
            return f"{self.count/10000:.1f}万"
        elif self.count >= 1000:
            return f"{self.count/1000:.1f}千"
        else:
            return str(self.count)


class CountResponseWrapper(StandardResponse[CountResponse]):
    """Standardized response for count endpoints."""
    pass


# ——— Export all schemas ———
__all__ = [
    # Migrated ranking request models
    "SchoolRanking",
    "UserRankingEveryRoute",
    "UserRankingTotalRoute",
    "TopNByRoute",
    # Migrated ranking response models
    "SchoolRankingResponse",
    "UserRankingEveryRouteResponse",
    "UserRankingTotalRouteResponse",
    "TopNByRouteResponse",
    "SummaryStatisticsResponse",
    # Migrated data models
    "SummaryStatistics",
    "SchoolRankingData",
    "UserRankingEveryRouteEachData",
    "UserRankingTotalRouteData",
    "TopNByRouteData",
    "SchoolRankingElement",
    "UserRankingEveryRouteElement",
    "UserRankingTotalRouteElement",
    # Original domain models
    "AnalyticsHealthResponse",
    "RankingFilters",
    "EventTrackingRequest",
    # ShenCe integration models
    "ShenceTagCreateRequest",
    "ShenceFileUploadRequest",
    "ShenceTagUpdateRequest",
    "ShenceDirectoryResponse",
    "ShenceTagMetaResponse",
    "ShenceUploadResponse",
    "ShenceTagResponse",
    # New Phase 2.3 schemas
    "DashboardData",
    "DashboardResponse",
    "CompetitionStats",
    "CompetitionStatsResponse",
    "SchoolStats",
    "SchoolStatsResponse",
    "UserStats",
    "UserStatsResponse",
    "CountResponse",
    "CountResponseWrapper",
]
