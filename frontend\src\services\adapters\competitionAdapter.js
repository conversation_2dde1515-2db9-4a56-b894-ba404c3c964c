/**
 * Competition Data Adapter
 * Transforms competition data between API and frontend formats
 */

import { BaseAdapter, AdapterRegistry } from './BaseAdapter.js'

export class CompetitionAdapter extends BaseAdapter {
  /**
   * Transform competition data from API to frontend format
   * @param {Object} competition - Competition data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed competition data
   */
  static transformItem(competition, type = 'default') {
    if (!competition) return null
    
    const base = super.transformItem(competition, type)
    
    switch (type) {
      case 'list':
        return {
          id: competition.id || competition.competition_id,
          competitionId: competition.competition_id || competition.id,
          title: competition.competition_name || competition.title,
          name: competition.competition_name || competition.title,
          routeId: competition.route_id,
          routeName: competition.route_name,
          status: competition.status,
          // Display fields
          displayName: competition.competition_name || competition.title,
          displayStatus: this.formatStatus(competition.status),
          // Dates
          startDate: competition.start_date || competition.startDate,
          endDate: competition.end_date || competition.endDate,
          // Stats
          totalParticipants: competition.total_participants,
          totalSubmissions: competition.total_submissions,
          ...base
        }
      
      case 'detail':
        return {
          id: competition.id || competition.competition_id,
          competitionId: competition.competition_id || competition.id,
          title: competition.competition_name || competition.title,
          name: competition.competition_name || competition.title,
          description: competition.description,
          routeId: competition.route_id,
          routeName: competition.route_name,
          status: competition.status,
          // Enhanced fields
          displayName: competition.competition_name || competition.title,
          displayStatus: this.formatStatus(competition.status),
          // Dates
          startDate: competition.start_date || competition.startDate,
          endDate: competition.end_date || competition.endDate,
          registrationDeadline: competition.registration_deadline || competition.registrationDeadline,
          // Stats
          stats: {
            totalParticipants: competition.total_participants,
            totalSubmissions: competition.total_submissions,
            averageScore: competition.average_score,
            completionRate: competition.completion_rate,
            ...competition.stats
          },
          // Additional info
          rules: competition.rules,
          prizes: competition.prizes,
          tags: competition.tags,
          ...base
        }
      
      case 'card':
        return {
          id: competition.id || competition.competition_id,
          title: competition.competition_name || competition.title,
          routeName: competition.route_name,
          status: competition.status,
          displayStatus: this.formatStatus(competition.status),
          participants: competition.total_participants,
          startDate: competition.start_date || competition.startDate,
          endDate: competition.end_date || competition.endDate,
          isActive: this.isActive(competition),
          canRegister: this.canRegister(competition)
        }
      
      case 'dropdown':
        return {
          value: competition.id || competition.competition_id,
          label: competition.competition_name || competition.title,
          routeId: competition.route_id,
          status: competition.status
        }
      
      default:
        return {
          id: competition.id || competition.competition_id,
          competitionId: competition.competition_id || competition.id,
          title: competition.competition_name || competition.title,
          routeId: competition.route_id,
          routeName: competition.route_name,
          status: competition.status,
          ...base
        }
    }
  }
  
  /**
   * Transform route data from API to frontend format
   * @param {Object} route - Route data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed route data
   */
  static transformRoute(route, type = 'default') {
    if (!route) return null

    // Handle multiple possible field names for route name
    const routeName = route.name || route.route_name || route.title
    const routeId = route.id || route.route_id

    switch (type) {
      case 'dropdown':
        return {
          value: routeId,
          label: routeName,
          description: route.description
        }

      case 'list':
        return {
          id: routeId,
          name: routeName,
          description: route.description,
          competitionCount: route.competition_count,
          isActive: route.is_active !== false
        }

      default:
        return {
          id: routeId,
          name: routeName,
          description: route.description
        }
    }
  }
  
  /**
   * Transform qualification data
   * @param {Object} qualification - Qualification data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed qualification data
   */
  static transformQualification(qualification, type = 'default') {
    if (!qualification) return null
    
    return {
      id: qualification.id,
      competitionId: qualification.competition_id,
      name: qualification.qualification_name,
      credit: qualification.credit,
      type: qualification.qualification_type,
      logic: qualification.qualification_logic,
      relatedTaskId: qualification.related_task_id,
      scoreThreshold: qualification.score_threshold,
      displayName: qualification.qualification_name,
      displayCredit: `${qualification.credit} credits`
    }
  }
  
  /**
   * Format competition status for display
   * @param {string} status - Competition status
   * @returns {string} Formatted status
   */
  static formatStatus(status) {
    const statusMap = {
      'active': 'Active',
      'upcoming': 'Upcoming',
      'completed': 'Completed',
      'cancelled': 'Cancelled',
      'draft': 'Draft'
    }
    
    return statusMap[status] || 'Unknown'
  }
  
  /**
   * Check if competition is currently active
   * @param {Object} competition - Competition data
   * @returns {boolean} True if active
   */
  static isActive(competition) {
    if (competition.status !== 'active') return false
    
    const now = new Date()
    const startDate = competition.start_date ? new Date(competition.start_date) : null
    const endDate = competition.end_date ? new Date(competition.end_date) : null
    
    if (startDate && now < startDate) return false
    if (endDate && now > endDate) return false
    
    return true
  }
  
  /**
   * Check if registration is open
   * @param {Object} competition - Competition data
   * @returns {boolean} True if can register
   */
  static canRegister(competition) {
    if (!this.isActive(competition)) return false
    
    const now = new Date()
    const deadline = competition.registration_deadline ? new Date(competition.registration_deadline) : null
    
    if (deadline && now > deadline) return false
    
    return true
  }
  
  /**
   * Transform competition list response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transform(response, options = {}) {
    const { type = 'list', isPaginated = false } = options
    
    if (isPaginated) {
      return this.transformPaginated(response, type)
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformItem(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformItem(response.data, type)
    }
  }
  
  /**
   * Transform routes response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transformRoutes(response, options = {}) {
    const { type = 'list' } = options
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformRoute(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformRoute(response.data, type)
    }
  }
  
  /**
   * Transform qualifications response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transformQualifications(response, options = {}) {
    const { type = 'list' } = options
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformQualification(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformQualification(response.data, type)
    }
  }
}

// Register the adapter
AdapterRegistry.register('competition', CompetitionAdapter)
AdapterRegistry.register('route', CompetitionAdapter)
AdapterRegistry.register('qualification', CompetitionAdapter)

export { CompetitionAdapter as competitionAdapter }
